### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

#### [3.2.0](https://github.com/coreui/coreui-free-react-admin-template/compare/3.1.1...3.2.0)

> 14 December 2020

- fix(BrandButtons): minor temp fixes with stylings [`f0c0585`](https://github.com/coreui/coreui-free-react-admin-template/commit/f0c05858329430c9487bdcfcf36d0aa98f60776d)
- refactor: polyfills cleanup [`9522ade`](https://github.com/coreui/coreui-free-react-admin-template/commit/9522ade1f1eb2c5a26d5208fe6e44be803123bd2)
- test: refactor, import temp update [`f3f91c5`](https://github.com/coreui/coreui-free-react-admin-template/commit/f3f91c5d721754a2dac26143f312415c94c7ed68)
- chore: React 17 and dependencies update [`f6a710f`](https://github.com/coreui/coreui-free-react-admin-template/commit/f6a710ffd6996c9b6b026b8eebed6ff390757fe1)
- fix(_nav): no anonymous default export [`ed83c9a`](https://github.com/coreui/coreui-free-react-admin-template/commit/ed83c9a3d4199b8c6566a6e01396cdcbca80cf42)

#### [3.1.1](https://github.com/coreui/coreui-free-react-admin-template/compare/3.1.0...3.1.1)

> 26 November 2020

- chore: dependencies update [`5b70904`](https://github.com/coreui/coreui-free-react-admin-template/commit/5b70904d8f22f61a71def3306ea96ad640573c39)
- chore: add changelog [`de605cc`](https://github.com/coreui/coreui-free-react-admin-template/commit/de605cc1aadec9fb066625c6bc7b656590efc7bb)
- chore: limit changelog to version 3 [`6ac9a1d`](https://github.com/coreui/coreui-free-react-admin-template/commit/6ac9a1d094d4d293ddacb3f3562216a98be8e129)
- chore: add migration docs [`b661344`](https://github.com/coreui/coreui-free-react-admin-template/commit/b661344485a38404b8c2b1326e3a4258009b071c)
- chore: changelog update [`f9f1927`](https://github.com/coreui/coreui-free-react-admin-template/commit/f9f19274f046cf5f11f68d6c7eb7ae04c553f219)
- chore: .gitignore cleanup [`ba21e00`](https://github.com/coreui/coreui-free-react-admin-template/commit/ba21e001944322fb48e43264719b94368c589732)
- chore: 3.1.1 release [`081b957`](https://github.com/coreui/coreui-free-react-admin-template/commit/081b957291a8020e3d2e3b42c1f0a1455a49c524)

#### [3.1.0](https://github.com/coreui/coreui-free-react-admin-template/compare/3.0.0...3.1.0)

> 12 August 2020

- docs(readme): CoreUI react theme setup with laravel tutorial link add into readme [`#226`](https://github.com/coreui/coreui-free-react-admin-template/pull/226)
- fix: fix template testing [`68ce41d`](https://github.com/coreui/coreui-free-react-admin-template/commit/68ce41db6831d6995121766a5771dc9d667cc61a)
- chore: 3.1.0 release - update dependencies [`92f55b8`](https://github.com/coreui/coreui-free-react-admin-template/commit/92f55b8cdfd748a9e72649d5da62b93015a1c8e6)
- refactor: add reusable folder with DocsLink component [`eef84db`](https://github.com/coreui/coreui-free-react-admin-template/commit/eef84dbbd770c7253080a6f69443c40e4fecefd2)
- feat: add CSwitch examples to forms [`b31e452`](https://github.com/coreui/coreui-free-react-admin-template/commit/b31e452fd0ea736763d3032d7204cd478863b505)
- refactor: add CIcon example in _nav.js [`be5d1f0`](https://github.com/coreui/coreui-free-react-admin-template/commit/be5d1f0618f981f18c45be87afb56c4409bd3389)

### [3.0.0](https://github.com/coreui/coreui-free-react-admin-template/compare/v2.6.1...3.0.0)

> 17 June 2020

- feat: update template to version 3 [`cc79542`](https://github.com/coreui/coreui-free-react-admin-template/commit/cc795425bbf610873fcdf6938b5fb0aba49a4d97)
- refactor: update folder casing to kebab-case [`75138b0`](https://github.com/coreui/coreui-free-react-admin-template/commit/75138b0d0340cc21d58bcc2f800f042f86e54346)
- refactor: temporarily delete views folder [`cb4433a`](https://github.com/coreui/coreui-free-react-admin-template/commit/cb4433a3e33cb943bc1f47199110ead28fab517b)
- docs: README update [`188e0b1`](https://github.com/coreui/coreui-free-react-admin-template/commit/188e0b1c09fd7d47dc87d0410303ae43e8ee79de)
- chore: clear packages [`e236aad`](https://github.com/coreui/coreui-free-react-admin-template/commit/e236aad4ab0129e3611adfc2127670da64696e54)
- fix: delete obsolete files, fix logos [`f479a5d`](https://github.com/coreui/coreui-free-react-admin-template/commit/f479a5dc72bb5bb75b95a4b904d1c350be8fe7bc)
- chore: 3.0.0-beta.1 release [`d940f92`](https://github.com/coreui/coreui-free-react-admin-template/commit/d940f92ef741d7eab021af4fbcf385823c80421a)
- fix: fix accordion, delete aside [`0e6506e`](https://github.com/coreui/coreui-free-react-admin-template/commit/0e6506ea3303ca30bc21ba2bcf3717a3f009dc8c)
- refactor: optimize icon bundle size, update icons [`9fed168`](https://github.com/coreui/coreui-free-react-admin-template/commit/9fed168a534b88cb27371d6364b922418a5a13b4)
- refacotor: template updates [`1df8c15`](https://github.com/coreui/coreui-free-react-admin-template/commit/1df8c15030d45779f6adc5031153eaff09701d97)
- refactor: turn logos extensions from svg to js [`8c0deee`](https://github.com/coreui/coreui-free-react-admin-template/commit/8c0deeed169267155323a5b6bdbbdfaf8a856a41)
- refactor: rename containers from 'Default' to 'The', small fixes [`bfc79da`](https://github.com/coreui/coreui-free-react-admin-template/commit/bfc79da4039dd534ee49b4526978f7b949cea90b)
- refactor: update icons to version 2, rtl fixes [`8e4fbc2`](https://github.com/coreui/coreui-free-react-admin-template/commit/8e4fbc2aa8786b00a004282260c52986e1cd2430)
- fix: delete unneded icons, aside, fix readme.md [`1ee0561`](https://github.com/coreui/coreui-free-react-admin-template/commit/1ee05619ba15d050b73df21c8d1347e8329942d5)
- chore: 3.0.0 version release - update dependencies [`fd5236d`](https://github.com/coreui/coreui-free-react-admin-template/commit/fd5236d47340b336bf641041cbf6d48ec8b1081a)
- feat: add query parameters to Users view [`98f8b67`](https://github.com/coreui/coreui-free-react-admin-template/commit/98f8b677edb96f9175b7d4c20370c3d6744543bd)
- docs: add license [`db85786`](https://github.com/coreui/coreui-free-react-admin-template/commit/db85786be465fdb7a84b7337dbe876afc5e957bc)
- chore: update react.md [`5aa0cc3`](https://github.com/coreui/coreui-free-react-admin-template/commit/5aa0cc3ce15c841032cd75392418cfeb2e4d094f)
- docs: README cleanup [`82a4351`](https://github.com/coreui/coreui-free-react-admin-template/commit/82a4351daa6c8d452e19c7141dbadecc3f721c1b)
- fix: fix Icons views [`1777a09`](https://github.com/coreui/coreui-free-react-admin-template/commit/1777a092f6444497120e85c8852a1e4779640e71)
