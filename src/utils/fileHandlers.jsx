import axios from "axios";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";

export const uploadImage = async (imageFile) => {
    try {
        const imageData = new FormData();
        imageData.append("file", imageFile);
        imageData.append("upload_preset", "ml_default");
        imageData.append("cloud_name", "ddzcwoooe");
        imageData.append("folders", "avatars");
        const { data } = await axios.post(
            "https://api.cloudinary.com/v1_1/ddzcwoooe/image/upload",
            imageData
        );
        const result = {
            publicId: data.public_id,
            secureUrl: data.secure_url,
        };
        return result;
    } catch (error) {
        toast.custom(<Danger message={error?.message ? error.message : "failed to upload the image file"} />)
        console.log(error);
    }
};
