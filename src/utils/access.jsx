const data = {
  admins: [
    "view_all_admins",
    "add_new_admin",
    "change_admin_status",
    "block_admin",
    "access_admin_logs",
  ],

  admins_filter: ["view_all_admins_filter"],
  stores: ["view_store", "create_store", "update_store", "delete_store"],
  trendingStores: [
    "create_trending_store",
    "view_all_trending_stores",
    "delete_trending_store",
    "update_trending_store",
  ],
  offers: ["view_offer", "add_new_offer", "edit_offer", "delete_offer"],
  giftCard: [
    "create_GIFT_CARD",
    "view_all_GIFT_CARDs",
    "view_GIFT_CARD_details",
    "update_GIFT_CARD",
    "delete_GIFT_CARD",
    "create_GIFT_CARD_sliders",
    "view_all_GIFT_CARD_sliders",
    "update_GIFT_CARD_slider",
    "delete_GIFT_CARD_slider",
    "create_GIFT_CARD_offers",
    "view_all_GIFT_CARDs_offers",
    "view_GIFT_CARD_offer_details",
    "update_GIFT_CARD_offer",
    "delete_GIFT_CARD_offer",
  ],
  accessControls: [
    "view_all_middlewares",
    "view_all_actions",
    "create_middleware",
    "update_middleware",
  ],
  stories: [
    "create_stories",
    "update_stories",
    "delete_stories",
    "get_all_stories",
  ],
  storeReviews: [
    "get_all_store_reviews",
    "update_store_reviews",
    "delete_store_reviews",
  ],
  storeCategories: [
    "create_storeCashback_rate",
    "create_storeCashback_rates",
    "view_common_storeCashback_rates",
    "update_storeCashback_rates",
    "delete_storeCashback_rates",
  ],
  // offers: ['view_all_cashback', 'create_cashback', 'edit_cashback', 'view_single_cashback', 'delete_cashback'],
  trendingOffers: [
    "create_trending_offer",
    "view_all_trending_offers",
    "delete_trending_offer",
    "update_trending_offer",
  ],
  clicks: ["get_clicks", "get_click_details"],
  approvedOrders: [
    "create_earnings",
    "update_earnings",
    "delete_earnings",
    "get_all_earnings",
    "get_earnings_details",
    "get_approved_earnings",
    "get_confirm_earnings",
    "get_cancel_earnings",
    "get_without_earnings",
  ],
  earnings: [
    "create_earnings",
    "update_earnings",
    "delete_earnings",
    "get_all_earnings",
    "get_earnings_details",
    "get_approved_earnings",
    "get_confirm_earnings",
    "get_cancel_earnings",
    "get_without_earnings",
  ],
  paymentRequests: [
    "create_payment_request",
    "update_payment_request",
    "delete_payment_request",
    "get_request_details",
    "get_all_payment_request",
  ],
  missingCashback: [
    "get_all_missing_cashback",
    "create_all_missing_cashback",
    "delete_missing_cashback",
    "update_missing_cashback",
    "get_missing_cashback_details",
  ],
  missedDeals: [
    "get_all_missed_deals",
    "create_missed_deals",
    "update_missed_deals",
    "delete_missed_deals",
  ],
  categories: [
    "view_categories",
    "add_new_category",
    "update_category",
    "delete_category",
    "restoreCategory",
    "add_new_subcategory",
    "remove_subcategory",
    "create_subcategory",
    "delete_subcategory",
    "update_name_subcategory",
  ],
  affiliations: [
    "view_all_affiliations",
    "create_affiliation",
    "update_affiliation",
    "delete_affiliation",
  ],
  users: ["view_all_users", "update_user_details"],
  testimonials: [
    "create_testimonials",
    "update_testimonials",
    "get_all_testimonials",
    "delete_testimonials",
  ],
  quickAccess: [
    "create_quick_access",
    "get_all_quick_accesses",
    "update_quick_accesses",
    "delete_quick_access",
  ],
  icbGiftCards: ["get_all_icb_giftcards", "delete_icb_giftcards"],
  banners: [
    "create_banner",
    "update_banner",
    "get_all_banners",
    "delete_banners",
  ],
  ongoingSales: [
    "create_ongoing_sales",
    "update_ongoing_sales",
    "get_all_ongoing_sales",
    "delete_ongoing_sales",
  ],
  termsAndPrivacy: [
    "create_terms_and_privacy",
    "update_terms_and_privacy",
    "get_all_terms_and_privacy",
    "delete_terms_and_privacy",
  ],
  personalInterest: [
    "create_personal_interest",
    "get_all_personal_interests",
    "update_personal_interest",
    "delete_personal_interest",
  ],
};

export const access = data;
