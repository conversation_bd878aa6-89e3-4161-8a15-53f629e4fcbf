import React from "react";

const Dashboard = React.lazy(() => import("./views/dashboard/Dashboard.jsx"));

//store
const AllStores = React.lazy(() => import("./views/stores/AllStores.jsx"));
const CreateStore = React.lazy(() => import("./views/stores/CreateStores.jsx"));
const EditStore = React.lazy(() => import("./views/stores/EditStore.jsx"));

// banners
const AllDesktopBanners = React.lazy(() =>
  import("./views/banners/banners/AllBanners.jsx")
);
const CreateDesktopBanners = React.lazy(() =>
  import("./views/banners/banners/CreateBanner.jsx")
);
const EditDesktopBanners = React.lazy(() =>
  import("./views/banners/banners/EditBanner.jsx")
);
const EditStories = React.lazy(() =>
  import("./views/banners/stories/EditStories.jsx")
);
const CreateStories = React.lazy(() =>
  import("./views/banners/stories/CreateStories.jsx")
);
const AllStories = React.lazy(() =>
  import("./views/banners/stories/AllStories.jsx")
);

// quick access
const AllQuickAccess = React.lazy(() =>
  import("./views/quickAccess/AllQuickAccess.jsx")
);
const EditQuickAccess = React.lazy(() =>
  import("./views/quickAccess/EditQuickAccess.jsx")
);
const CreateQuickAccess = React.lazy(() =>
  import("./views/quickAccess/CreateQuickAccess.jsx")
);
// affiliations
const AllAffiliations = React.lazy(() =>
  import("./views/affiliation/allAffiliations/AllAffiliations.jsx")
);
const CreateAffiliation = React.lazy(() =>
  import("./views/affiliation/createAffiliation/CreateAffiliation.jsx")
);
const EditAffiliation = React.lazy(() =>
  import("./views/affiliation/editAffiliation/EditAffiliation.jsx")
);
// trending stores
const TrendingStores = React.lazy(() =>
  import("./views/stores/trending/allTrending/AllTrending.jsx")
);

// store reviews
const AllStoreReviews = React.lazy(() =>
  import("./views/stores/reviews/allReviews/AllReviews.jsx")
);

// store categories
const AllStoreCategories = React.lazy(() =>
  import("./views/stores/categories/allCategories/AllCategories.jsx")
);
const CreateStoreCategory = React.lazy(() =>
  import("./views/stores/categories/createCategory/CreateCategory.jsx")
);
const EditStoreCategory = React.lazy(() =>
  import("./views/stores/categories/editCategory/EditCategory.jsx")
);
const StoreCategoryHistory = React.lazy(() =>
  import("./views/stores/categories/histories/Histories.jsx")
);

//offers
const AllOffers = React.lazy(() =>
  import("./views/offers/allOffers/AllOffers.jsx")
);
const CreateOffer = React.lazy(() =>
  import("./views/offers/createOffers/Create.jsx")
);
const EditOffer = React.lazy(() =>
  import("./views/offers/editOffer/EditOffer.jsx")
);

const TrendingOffers = React.lazy(() =>
  import("./views/offers/TrendingOffers/TrendingOffers.jsx")
);

// on going sales
const AllOngoingSales = React.lazy(() =>
  import("./views/offers/ongoingSales/AllOngoingSales.jsx")
);
const CreateOngoingSale = React.lazy(() =>
  import("./views/offers/ongoingSales/CreateOngoingSale.jsx")
);
const EditOngoingSale = React.lazy(() =>
  import("./views/offers/ongoingSales/EditOngoingSale.jsx")
);

// icb gift card
const AllIcbGiftCardOrders = React.lazy(() =>
  import("./views/icb-giftcard/allIcbGiftCardOrders/AllIcbGiftCardOrders.jsx")
);
// payments
const AllMissingCashback = React.lazy(() =>
  import("./views/payments/missings/allMissingCashbacks/AllMissingCashbacks.jsx")
);
const AllPaymentRequest = React.lazy(() =>
  import("./views/payments/requests/allRequests/AllRequests.jsx")
);

// admins
const AllAdmins = React.lazy(() =>
  import("./views/admins/allAdmins/AllAdmins.jsx")
);
const CreateAdmin = React.lazy(() =>
  import("./views/admins/createAdmin/CreateAdmin.jsx")
);
const UpdateAdmin = React.lazy(() =>
  import("./views/admins/updateAdmin/UpdateAdmin.jsx")
);
const AdminLogs = React.lazy(() => import("./views/admins/logs/Logs.jsx"));

// middlewares (access controls)
const AllMiddlewares = React.lazy(() =>
  import("./views/middlewares/allMiddlewares/AllMiddleware.jsx")
);
const CreateMiddleware = React.lazy(() =>
  import("./views/middlewares/createMiddlewares/CreateMiddleware.jsx")
);

// gift cards
const AllGiftCards = React.lazy(() =>
  import("./views/giftcards/giftcards/allGiftcards/AllGiftcards.jsx")
);
const CreateGiftCards = React.lazy(() =>
  import("./views/giftcards/giftcards/createGiftcard/CreateGiftcard.jsx")
);
const UpdateGiftCard = React.lazy(() =>
  import("./views/giftcards/giftcards/updateGiftcard/UpdateGiftcard.jsx")
);

// gift card sliders
const AllGiftCardSliders = React.lazy(() =>
  import("./views/giftcards/sliders/allSliders/AllSliders.jsx")
);
const CreateGiftCardSlider = React.lazy(() =>
  import("./views/giftcards/sliders/createSliders/CreateSliders.jsx")
);
const UpdateGiftCardSlider = React.lazy(() =>
  import("./views/giftcards/sliders/updateSliders/UpdateSliders.jsx")
);

// gift cards offers
const AllGiftCardOffers = React.lazy(() =>
  import("./views/giftcards/offers/allOffers/AllOffers.jsx")
);
const CreateGiftCardOffer = React.lazy(() =>
  import("./views/giftcards/offers/createOffer/CreateOffer.jsx")
);
const UpdateGiftCardOffer = React.lazy(() =>
  import("./views/giftcards/offers/updateOffer/UpdateOffer.jsx")
);

// gift card orders
const AllGiftCardOrders = React.lazy(() =>
  import("./views/giftcards/orders/allOrders/AllOrders.jsx")
);

// categories and sub categories
const AllCategories = React.lazy(() =>
  import("./views/categories/allCategories/AllCategories.jsx")
);
const CreateCategory = React.lazy(() =>
  import("./views/categories/createCategory/CreateCategory.jsx")
);
const EditCategory = React.lazy(() =>
  import("./views/categories/updateCategory/UpdateCategory.jsx")
);
const AllSubCategory = React.lazy(() =>
  import("./views/categories/subcategories/allSubCategories/AllCategories.jsx")
);
const CreateSubCategory = React.lazy(() =>
  import(
    "./views/categories/subcategories/createSubCategories/CreateCategories.jsx"
  )
);
const UpdateSubCategory = React.lazy(() =>
  import("./views/categories/subcategories/editSubCategories/EditSubCategory.jsx")
);

// users
const Users = React.lazy(() => import("./views/users/Users.jsx"));
const AllUsers = React.lazy(() => import("./views/users/user/AllUsers.jsx"));
const AllPersonalInterests = React.lazy(() =>
  import("./views/personal-interest/all-personal-interest/AllPersonalInterest.jsx")
);

const CreateEarnings = React.lazy(() =>
  import("./views/earnings/createEarnings/CreateEarnings.jsx")
);
const AllEarnings = React.lazy(() =>
  import("./views/earnings/allEarnings/AllEarnings.jsx")
);
const TrackedForCancelEarings = React.lazy(() =>
  import("./views/earnings/trackedForCancel/TrackedForCancel.jsx")
);
const TrackedForConfirmEarings = React.lazy(() =>
  import("./views/earnings/trackedForConfirm/TrackedForConfirm.jsx")
);
const PendingEarnings = React.lazy(() =>
  import("./views/earnings/pendingEarnings/PendingEarnings.jsx")
);
const IndividualEarnings = React.lazy(() =>
  import("./views/earnings/IndividualEarnings/IndividualEarnings.jsx")
);
const EditEarnings = React.lazy(() =>
  import("./views/earnings/editEarnings/EditEarnings.jsx")
);
const PreApprovedEarnings = React.lazy(() =>
  import("./views/earnings/pendingEarnings/PendingEarnings.jsx")
);

const AllClicks = React.lazy(() =>
  import("./views/clicks/allClicks/AllClicks.jsx")
);
const AllAutoTracked = React.lazy(() =>
  import("./views/auto-tracked/AllAutoTracked/AllAutoTracked.jsx")
);

const AllTermsAndPrivacies = React.lazy(() =>
  import("./views/termsAndPrivacy/allTermsAndPrivacies/AllTermsAndPrivacies.jsx")
);
const CreateTermsAndPrivacy = React.lazy(() =>
  import("./views/termsAndPrivacy/createTermsAndPrivacy/CreateTermsAndPrivacy.jsx")
);
const EditTermsAndPrivacy = React.lazy(() =>
  import("./views/termsAndPrivacy/editTermsAndPrivacy/EditTermsAndPrivacy.jsx")
);

const AllTermsAndPrivacyLogs = React.lazy(() =>
  import("./views/termsAndPrivacy/termsAndPrivacyLogs/TermsAndPrivacyLogs.jsx")
);

// testimonials
const AllTestimonials = React.lazy(() =>
  import("./views/testimonials/allTestimonials/AllTestimonials.jsx")
);

const CreateTestimonials = React.lazy(() =>
  import("./views/testimonials/createTestimonial/CreateTestimonial.jsx")
);

const EditTestimonials = React.lazy(() =>
  import("./views/testimonials/updateTestimonial/UpdateTestimonial.jsx")
);

// charts
const Charts = React.lazy(() => import("./views/charts/Charts.jsx"));
// Icons

const routes = [
  { path: "/", exact: true, name: "Home" },

  { path: "/admins", name: "Admin", element: AllAdmins },
  {
    path: "/admins/all-admins",
    name: "All Admins",
    element: AllAdmins,
    exact: true,
  },
  {
    path: "/admins/create-admin",
    name: "Create Admin",
    element: CreateAdmin,
    exact: true,
  },
  {
    path: "/admins/super-admins",
    name: "Super Admins",
    element: AllAdmins,
    exact: true,
  },
  {
    path: "/admins/edit-admin/:adminId",
    name: "Edit  Admin",
    element: UpdateAdmin,
    exact: true,
  },
  { path: "/admins/logs", name: "Admin Logs", element: AdminLogs, exact: true },

  { path: "/access-controls", name: "Access Controls", element: Users },
  {
    path: "/access-controls/all-access-controls",
    name: "All Access Controls",
    element: AllMiddlewares,
    exact: true,
  },
  {
    path: "/access-controls/create-access-controls",
    name: "create Access Controls",
    element: CreateMiddleware,
    exact: true,
  },

  { path: "/store", name: "Store", element: AllStores },
  {
    path: "/store/all-stores/:storeId",
    name: "All Stores",
    element: AllStores,
    exact: true,
  },
  {
    path: "/store/create-store",
    name: "Create Stores",
    element: CreateStore,
    exact: true,
  },
  {
    path: "/store/store",
    name: "Single Store",
    element: AllStores,
    exact: true,
  },
  {
    path: "/store/edit-store/:storeId",
    name: "Edit Store",
    element: EditStore,
    exact: true,
  },
  { path: "/banners", name: "Banners", element: AllDesktopBanners },
  {
    path: "/banners/desktop/all-stories",
    name: "All Desktop Banners",
    element: AllDesktopBanners,
    exact: true,
  },
  {
    path: "/banners/desktop/create",
    name: "Create Desktop Banners",
    element: CreateDesktopBanners,
    exact: true,
  },
  {
    path: "/banners/desktop/edit/:bannerId",
    name: "Edit Desktop Banners",
    element: EditDesktopBanners,
    exact: true,
  },
  {
    path: "/banners/stories/all-stories",
    name: "All Mobile Stories",
    element: AllStories,
    exact: true,
  },
  {
    path: "/banners/stories/create",
    name: "Create Mobile Stories",
    element: CreateStories,
    exact: true,
  },
  {
    path: "/banners/stories/edit/:storyId",
    name: "Edit Mobile Stories",
    element: EditStories,
    exact: true,
  },
  { path: "/quick-access", name: "Quick Access", element: AllQuickAccess },
  {
    path: "/quick-access/all-quick-access",
    name: "All Quick Access",
    element: AllQuickAccess,
    exact: true,
  },
  {
    path: "/quick-access/create",
    name: "Create Quick Access",
    element: CreateQuickAccess,
    exact: true,
  },
  {
    path: "/quick-access/edit/:quickAccessId",
    name: "Edit Quick Access",
    element: EditQuickAccess,
    exact: true,
  },
  { path: "/affiliation", name: "Affiliation", element: AllAffiliations },
  {
    path: "/affiliation/all-affiliations",
    name: "All Affiliations",
    element: AllAffiliations,
    exact: true,
  },
  {
    path: "/affiliation/create-affiliation",
    name: "Create Affiliation",
    element: CreateAffiliation,
    exact: true,
  },
  {
    path: "/affiliation/update/:affiliationId",
    name: "Update Affiliation",
    element: EditAffiliation,
    exact: true,
  },
  {
    path: "/store/trending-stores",
    name: "Trending Stores",
    element: TrendingStores,
    exact: true,
  },

  { path: "/store-reviews", name: "Store Reviews", element: AllStoreReviews },
  {
    path: "/store-reviews/all-store-reviews",
    name: "All Store Reviews",
    element: AllStoreReviews,
    exact: true,
  },

  {
    path: "/store-categories",
    name: " Store Categories",
    element: AllStoreCategories,
  },
  {
    path: "/store-categories/all-store-categories",
    name: "All Store Categories",
    element: AllStoreCategories,
  },
  {
    path: "/store-categories/create-store-category",
    name: "Edit Store Category",
    element: CreateStoreCategory,
  },
  {
    path: "/store-categories/edit-store-category/:categoryId",
    name: "Edit Store Category",
    element: EditStoreCategory,
  },
  {
    path: "/store-categories/category-histories",
    name: "Store Category Histories",
    element: StoreCategoryHistory,
  },

  { path: "/offers", name: "Offers", element: AllOffers },
  {
    path: "/offers/all-offers",
    name: "All Offers",
    element: AllOffers,
    exact: true,
  },
  {
    path: "/offers/ongoing-sales/all-sales",
    name: "All Ongoing Sales",
    element: AllOngoingSales,
    exact: true,
  },
  {
    path: "/offers/ongoing-sales/create",
    name: "Create Ongoing Sale",
    element: CreateOngoingSale,
    exact: true,
  },
  {
    path: "/offers/ongoing-sales/edit/:saleId",
    name: "Edit Ongoing Sale",
    element: EditOngoingSale,
    exact: true,
  },
  {
    path: "/offers/trending-offers",
    name: "Trending Offers",
    element: TrendingOffers,
    exact: true,
  },
  {
    path: "/offers/create-offer",
    name: "Create Offer",
    element: CreateOffer,
    exact: true,
  },
  {
    path: "/offers/edit-offer/:offerId",
    name: "Edit Offer",
    element: EditOffer,
    exact: true,
  },
  {
    path: "/clicks/all-clicks",
    name: " Clicks",
    element: AllClicks,
    exact: true,
  },
  {
    path: "/auto-tracked/all-auto-tracked",
    name: "AutoTracked",
    element: AllAutoTracked,
    exact: true,
  },

  { path: "/earnings", name: " Earnings", element: AllEarnings },
  {
    path: "/earnings/pre-approved",
    name: " Pre Approved Earnings",
    element: PreApprovedEarnings,
  },
  {
    path: "/earnings/all-earnings",
    name: "All Approved Earnings",
    element: AllEarnings,
    exact: true,
  },

  { path: "/payments", name: "Payments", element: AllPaymentRequest },
  {
    path: "/payments/requests/all-requests",
    name: "Payments Requests",
    element: AllPaymentRequest,
    exact: true,
  },
  {
    path: "/payments/missing/all-cashbacks",
    name: "Missing Cashback",
    element: AllMissingCashback,
    exact: true,
  },

  { path: "/category", name: "Category", element: AllCategories },
  {
    path: "/category/all-categories",
    name: "All Category",
    element: AllCategories,
    exact: true,
  },
  {
    path: "/category/create-category",
    name: "Create Category",
    element: CreateCategory,
    exact: true,
  },
  {
    path: "/category/edit-category/:categoryId",
    name: "Edit Category",
    element: EditCategory,
    exact: true,
  },
  {
    path: "/category/all-sub-category/:categoryId",
    name: "All Sub Category",
    element: AllSubCategory,
    exact: true,
  },
  {
    path: "/category/create-sub-category",
    name: "Create Sub Category",
    element: CreateSubCategory,
    exact: true,
  },
  {
    path: "/category/update-sub-category/:categoryId",
    name: "Create Sub Category",
    element: UpdateSubCategory,
    exact: true,
  },

  { path: "/giftcards", name: "Gift Cards", element: AllGiftCards },
  {
    path: "/giftcards/all-giftcards",
    name: "All Gift Cards",
    element: AllGiftCards,
    exact: true,
  },
  {
    path: "/giftcards/create-giftcard",
    name: "Create Gift Cards",
    element: CreateGiftCards,
    exact: true,
  },
  {
    path: "/giftcards/update-giftcard/:giftcardId",
    name: "Update Gift Cards",
    element: UpdateGiftCard,
    exact: true,
  },

  { path: "/giftcards/sliders", name: "Sliders", element: AllGiftCards },
  {
    path: "/giftcards/sliders/all-sliders",
    name: "All Sliders",
    element: AllGiftCardSliders,
    exact: true,
  },
  {
    path: "/giftcards/sliders/create-sliders",
    name: "Create Sliders",
    element: CreateGiftCardSlider,
    exact: true,
  },
  {
    path: "/giftcards/sliders/update-sliders/:sliderId",
    name: "Update Sliders",
    element: UpdateGiftCardSlider,
    exact: true,
  },

  { path: "/giftcards/offers", name: "Offers", element: AllGiftCards },
  {
    path: "/giftcards/offers/all-offers",
    name: "All Offers",
    element: AllGiftCardOffers,
    exact: true,
  },
  {
    path: "/giftcards/offers/create-offers",
    name: "Create Offers",
    element: CreateGiftCardOffer,
    exact: true,
  },
  {
    path: "/giftcards/offers/update-offers/:offerId",
    name: "Update Offers",
    element: UpdateGiftCardOffer,
    exact: true,
  },

  { path: "/giftcards/orders", name: "Orders", element: AllGiftCards },
  {
    path: "/giftcards/orders/all-orders",
    name: "All Orders",
    element: AllGiftCardOrders,
    exact: true,
  },
  {
    path: "/icb-giftcards/all-orders",
    name: "All Icb GiftCard Orders",
    element: AllIcbGiftCardOrders,
    exact: true,
  },

  { path: "/users", name: "Users", element: Users },
  {
    path: "/users/all-users",
    name: "All Users",
    element: AllUsers,
    exact: true,
  },
  {
    path: "/users/all-personal-interests",
    name: "Personal Interests",
    element: AllPersonalInterests,
    exact: true,
  },
  {
    path: "/users/clicks",
    name: "User Clicks",
    element: AllClicks,
    exact: true,
  },
  {
    path: "/users/auto-tracked",
    name: "Auto Tracked",
    element: AllAutoTracked,
    exact: true,
  },
  {
    path: "/earnings/pending",
    name: "Pending Earnings",
    element: PendingEarnings,
    exact: true,
  },
  {
    path: "/earnings/tracked-for-confirm",
    name: "Tracked For Confirm",
    element: TrackedForConfirmEarings,
    exact: true,
  },
  {
    path: "/earnings/tracked-for-cancel",
    name: "Tracked For Cancel",
    element: TrackedForCancelEarings,
    exact: true,
  },
  {
    path: "/earnings/individual-earnings/:storeId/:userId",
    name: "Individual Earnings",
    element: IndividualEarnings,
    exact: true,
  },
  {
    path: "/earnings/create/:clickId/:earningsType",
    name: "User Earnings",
    element: CreateEarnings,
  },
  {
    path: "/earnings/edit/:earningId",
    name: "User Earnings",
    element: EditEarnings,
  },
  {
    path: "/testimonials",
    name: "Testimonials",
    element: AllTestimonials,
    exact: true,
  },
  {
    path: "/testimonials/all-testimonials",
    name: "All Testimonials",
    element: AllTestimonials,
  },
  {
    path: "/testimonials/create",
    name: "Create Testimonial",
    element: CreateTestimonials,
  },
  {
    path: "/testimonials/edit/:testimonialId",
    name: "Edit Testimonial",
    element: EditTestimonials,
  },
  //
  {
    path: "/terms-and-privacy",
    name: "Terms And Privacy",
    element: AllTermsAndPrivacies,
    exact: true,
  },
  {
    path: "/terms-and-privacy/all-terms-privacies",
    name: "All Terms And Privacies",
    element: AllTermsAndPrivacies,
  },
  {
    path: "/terms-and-privacy/create",
    name: "Create Terms And Privacy",
    element: CreateTermsAndPrivacy,
  },
  {
    path: "/terms-and-privacy/edit/:id",
    name: "Edit Terms And Privacy",
    element: EditTermsAndPrivacy,
  },
  {
    path: "/terms-and-privacy/logs",
    name: "Terms And Privacy Logs",
    element: AllTermsAndPrivacyLogs,
  },

  { path: "/dashboard", name: "Dashboard", element: Dashboard },

  { path: "/charts", name: "Charts", element: Charts },
];

export default routes;
