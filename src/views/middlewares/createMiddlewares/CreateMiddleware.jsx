import React, { useEffect } from "react";
import { <PERSON><PERSON>on, <PERSON>orm, <PERSON><PERSON><PERSON>, CRow, CCol } from "@coreui/react";
import { useNavigate } from "react-router-dom";
import LoadingGif from "src/components/alerts/loading/Loading";
import { createMiddleware, fetchAllMiddlewares } from "src/redux/features";
import { useDispatch, useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";

const schema = yup
  .object({
    name: yup.string("").required("name is a required filed"),
    level: yup.number("level is number type").required("level is a required filed"),
  })
  .required();

const CreateMiddleware = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const { allMiddlewares, loading } = useSelector((state) => state.middleware);

  useEffect(() => {
    dispatch(fetchAllMiddlewares({}));
  }, [dispatch]);

  const onSubmit = async (data) => {
    dispatch(createMiddleware(data));
  };
  return (
    <CContainer fluid>
      <CRow>
        <CCol className="mx-auto" md={6} lg={6} xl={6}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <h2>Create New Access Controls </h2>
            <InputField
              state={"name"}
              title={"Access Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <InputField
              state={"level"}
              title={"Level"}
              type={"text"}
              setState={register}
              error={errors?.level?.message}
            />

            <div className="d-flex justify-center ">
              <CButton
                type="reset"
                onClick={() => navigate("/access-controls/all-access-controls")}
                className="ms-auto mx-1 text-light"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                type="submit"
                className="me-auto mx-1 text-light"
                color="success"
              >
                Create{" "}
              </CButton>
            </div>
          </CForm>
          {loading && <LoadingGif />}
        </CCol>
        <CCol
          className="mx-auto bg-white border rounded shadow p-3"
          md={4}
          lg={4}
          xl={4}
        >
          <h5>Controls and Levels</h5>
          {allMiddlewares?.map((item) => (
            <p key={item._id}>
              {item?.level} : {item?.name}
            </p>
          ))}
        </CCol>
      </CRow>
    </CContainer>
  );
};

export default CreateMiddleware;
