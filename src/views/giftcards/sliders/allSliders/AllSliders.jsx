import { cilArrow<PERSON>ottom, cilPen, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import "../../giftcard.css";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteGiftCardSlider,
  fetchAllGiftCardSliders,
  handleOpenModal,
  updateGiftCardSliderPriority,
} from "src/redux/features";

import ReadMore from "src/components/text/ReadMore";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllSliders() {
  const [search, setSearch] = useState("");
  const [lowPriority, setLowPriority] = useState(false);
  const [HiPriority, setHiPriority] = useState(false);
  const [expire, setExpire] = useState(false);
  const [sortName, setSortName] = useState(false);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { page, pages, pageSize, allGiftCardSliders, loading } = useSelector(
    (state) => state.giftCardSlider
  );

  useEffect(() => {
    dispatch(fetchAllGiftCardSliders({}));
  }, [dispatch]);

  const handleClickPriority = async (sliderId, priority) => {
    dispatch(updateGiftCardSliderPriority({ sliderId, priority }));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllGiftCardSliders({ search: value }));
  };
  const handlePagination = async (value) => {
    dispatch(fetchAllGiftCardSliders({ page: value }));
  };

  const handleSort = async (value) => {};
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol sm={12} md={6}>
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="my-2 p-1 px-2 rounded border"
            placeholder="Search..."
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol className="text-end ms-auto" sm={12} md={6}>
          <button
            type="button"
            onClick={() => navigate("/giftcards/sliders/create-sliders")}
            className=" my-2 btn btn-primary mx-auto btn-sm"
          >
            Create Giftcard Slider
          </button>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  ms-auto   " sm={8}>
          <button
            type="button"
            className={` ${
              sortName ? " border-bottom border-primary border-3 bg-none" : " "
            }  border-0  mx-2 `}
            onClick={() => handleSort("name")}
          >
            Name{" "}
            <CIcon
              className={`${sortName ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              HiPriority ? "border-bottom border-primary border-3 bg-none" : ""
            }  border-0 mx-2  `}
            onClick={() => handleSort("prioTrue")}
          >
            Highest Priority{" "}
            <CIcon
              className={`${HiPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              lowPriority
                ? "border-bottom border-primary border-3 bg-none"
                : " "
            } border-0  mx-2  `}
            onClick={() => handleSort("prioFasle")}
          >
            Lowest Priority{" "}
            <CIcon
              className={`${lowPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              expire ? "border-bottom border-primary border-3 bg-none" : " "
            } border-0  mx-2  `}
            onClick={() => handleSort("expire")}
          >
            Expire First{" "}
            <CIcon
              className={`${expire ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CTable
        align="middle"
        className="mb-0 border"
        hover
        striped
        bordered
        borderColor="secondary"
        responsive
      >
        <CTableHead color="dark">
          <CTableRow>
            <CTableHeaderCell className="text-center"> No </CTableHeaderCell>
            <CTableHeaderCell>Desktop Slider</CTableHeaderCell>
            <CTableHeaderCell>Mobile Slider</CTableHeaderCell>
            <CTableHeaderCell> Terms title</CTableHeaderCell>
            <CTableHeaderCell> Terms Content</CTableHeaderCell>
            <CTableHeaderCell>Redirect Url</CTableHeaderCell>
            <CTableHeaderCell>Priority</CTableHeaderCell>
            <CTableHeaderCell>Added Date</CTableHeaderCell>
            <CTableHeaderCell>Expire Date</CTableHeaderCell>
            <CTableHeaderCell>Created By</CTableHeaderCell>
            <CTableHeaderCell>Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {!loading &&
            allGiftCardSliders?.map((item) => (
              <CTableRow
                v-for="item in tableItems"
                color={!item.active && "danger"}
                key={item._id}
              >
                <CTableDataCell className="text-center">
                  {item?.uid}
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.desktopBanner?.secureUrl,
                          })
                        )
                      }
                      width={70}
                      src={item?.desktopBanner?.secureUrl}
                      alt=""
                    />
                  </div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.mobileBanner?.secureUrl,
                          })
                        )
                      }
                      width={70}
                      src={item?.mobileBanner?.secureUrl}
                      alt=""
                    />
                  </div>
                </CTableDataCell>
                <CTableDataCell>
                  <div>{item?.termsTitle}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div>{item?.termsContent}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div>
                    {" "}
                    <ReadMore text={item.redirectUrl} />
                  </div>
                </CTableDataCell>

                <CTableDataCell>
                  <div className="text-center ">
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="border w-100 rounded py-2 px-1"
                      value={item?.priority}
                      onChange={(e) =>
                        handleClickPriority(item._id, e.target.value)
                      }
                    />
                  </div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {" "}
                    {dateFormatter(item.createdAt)}
                  </div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {" "}
                    {dateFormatter(item.expiryDate)}
                  </div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center"> {item?.createdBy?.name}</div>
                </CTableDataCell>
                <CTableDataCell className="text-center ">
                  <CTooltip content="edit giftcard sliders">
                    <button
                      type="button"
                      onClick={() =>
                        navigate(
                          `/giftcards/sliders/update-sliders/${item._id}`
                        )
                      }
                      className="border rounded shadow px-2 py-1 text-primary m-1"
                    >
                      <CIcon icon={cilPen} />
                    </button>
                  </CTooltip>
                  <CTooltip content="delete giftcard sliders">
                    <button
                      type="button"
                      onClick={() => dispatch(deleteGiftCardSlider(item._id))}
                      className={`${
                        item.active ? " text-secondary" : "text-danger"
                      } border rounded shadow px-2 py-1 m-1 `}
                    >
                      {" "}
                      <CIcon icon={cilTrash} />
                    </button>
                  </CTooltip>
                </CTableDataCell>
              </CTableRow>
            ))}
        </CTableBody>
      </CTable>
      {loading && <LoadingComponent />}
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllSliders;
