import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>r, <PERSON>orm, CRow } from "@coreui/react";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useNavigate, useParams } from "react-router-dom";
import "../../giftcard.css";
import InputField from "src/components/inputs/InputField";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllGiftCardsList,
  fetchGiftCardOfferDetails,
  updateGiftCardOffer,
} from "src/redux/features";
import { dateInDDMMYYYYFormat } from "src/helperFunctions/dateFormatter";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const currentDateTime = new Date().toISOString().slice(0, 16);
const schema = yup
  .object({
    name: yup.string().required("giftcard offer name is a required field"),
    rateGetting: yup.number().required("rate getting  is a required field"),
    rateGiving: yup.number().required(" rate giving  is a required field"),
    startDate: yup.date().required(" start date is a required field"),
    endDate: yup.date().required(" end date is a required field"),
  })
  .required();

function EditGiftCardOffer() {
  const [terms, setTerms] = useState("");
  const [giftCard, setGiftCard] = useState("");
  const [defaultGiftCard, setDefaultGiftCard] = useState();
  const { offerId } = useParams();

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { allGiftCardsList } = useSelector((state) => state.giftCard);
  const { giftCardOfferDetails } = useSelector((state) => state.giftCardOffer);

  useEffect(() => {
    dispatch(fetchAllGiftCardsList({}));
    if (offerId) {
      dispatch(fetchGiftCardOfferDetails(offerId));
    }
  }, [dispatch, offerId]);

  useEffect(() => {
    if (giftCardOfferDetails) {
      setDefaultGiftCard({
        label: giftCardOfferDetails?.giftCard?.name,
        value: giftCardOfferDetails?.giftCard?._id,
      });
      setTerms(giftCardOfferDetails?.terms);
      reset(
        {
          name: giftCardOfferDetails?.name,
          rateGetting: giftCardOfferDetails?.rateGetting,
          rateGiving: giftCardOfferDetails?.rateGiving,
          startDate: dateInDDMMYYYYFormat(giftCardOfferDetails?.startDate),
          endDate: dateInDDMMYYYYFormat(giftCardOfferDetails?.endDate),
        },
        { keepDirty: true, keepErrors: true }
      );
    }
  }, [giftCardOfferDetails]);

  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = (data) => {
    if (!terms) {
      return toast.custom(
        <Danger message={"please add how to use and terms and conditions"} />
      );
    }
    if (!giftCard) {
      return toast.custom(<Danger message={"please select a giftcard"} />);
    }

    const payload = {
      ...data,
      terms,
      giftCard,
    };
    dispatch(updateGiftCardOffer({ offerId, payload }));
  };
  const handleSelectGiftCard = (values) => {
    setGiftCard(values?.value);
  };
  console.log(errors);
  return (
    <CContainer fluid>
      <CRow className="justify-content-center">
        <h3 className="text-center">Create New Giftcard Offer</h3>
        <CCol md={9} lg={9} xl={9} className="rounded shadow my-3  ">
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <CRow>
              <InputField
                state={"name"}
                title={"Giftcard  Offer Name"}
                type={"text"}
                setState={register}
                error={errors?.name?.message}
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto text-center">Select Giftcard</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    defaultValue={defaultGiftCard}
                    array={allGiftCardsList}
                    handleSelectedValue={handleSelectGiftCard}
                    placeholder={"Select GiftCards..."}
                  />
                </CCol>
              </CRow>

              <InputField
                state={"rateGetting"}
                title={"Discount Getting"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.rateGetting?.message}
              />
              <InputField
                state={"rateGiving"}
                title={"Discount Giving"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.rateGiving?.message}
              />
              <InputField
                state={"startDate"}
                title={"Start Date"}
                type={"datetime-local"}
                setState={register}
                error={errors?.startDate?.message}
                currentDateTime={currentDateTime}
              />
              <InputField
                state={"endDate"}
                title={"Expiry Date"}
                type={"datetime-local"}
                setState={register}
                error={errors?.endDate?.message}
                currentDateTime={currentDateTime}
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Terms and Conditions</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={terms} setData={setTerms} />
                </CCol>
              </CRow>
            </CRow>
            <div className="text-center  mb-4">
              <CButton
                type="reset"
                onClick={() => navigate("/giftcards/offers/all-offers")}
                className="text-light ms-auto mx-1"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                type="submit"
                className="text-light me-auto mx-1"
                color="success"
              >
                Update
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default EditGiftCardOffer;
