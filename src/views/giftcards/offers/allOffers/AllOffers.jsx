import { cilArrow<PERSON>ottom, cilPen, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import "../../giftcard.css";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteGiftCardOffer,
  fetchAllGiftCardOffers,
  fetchAllGiftCardsList,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllOffers() {
  const [search, setSearch] = useState("");
  const [expire, setExpire] = useState(false);
  const [sortName, setSortName] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { page, pages, pageSize, loading, allGiftCardOffers } = useSelector(
    (state) => state.giftCardOffer
  );
  const { allGiftCardsList } = useSelector((state) => state.giftCard);

  useEffect(() => {
    dispatch(fetchAllGiftCardOffers({}));
    dispatch(fetchAllGiftCardsList({}));
  }, [dispatch]);

  const handleDelete = async (id) => {
    dispatch(deleteGiftCardOffer(id));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllGiftCardOffers({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllGiftCardOffers({ page: value }));
  };

  const handleSort = async (value) => {};
  const handleSelectGiftCard = (values) => {
    dispatch(fetchAllGiftCardOffers({ giftCard: values?.value }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol className="" sm={6}>
          <input
            type="text "
            onChange={(e) => handleSearch(e.target.value)}
            className=" rounded border p-2"
            placeholder="Search..."
          />
        </CCol>
      </CRow>
      <CRow className="">
        <CCol className="my-2  d-flex" sm={6}>
          <SearchAndSelect
            array={allGiftCardsList}
            handleSelectedValue={handleSelectGiftCard}
            placeholder={"Select GiftCard..."}
          />
        </CCol>
        <CCol className="  my-2 text-end" sm={6} md={6}>
          <button
            type="button"
            onClick={() => navigate("/giftcards/offers/create-offers")}
            className="mx-auto btn btn-primary"
          >
            Create Giftcard Offer
          </button>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  ms-auto   " sm={8}>
          <button
            type="button"
            className={` ${
              sortName ? " border-bottom border-primary border-3 bg-none" : " "
            }  border-0  mx-2 `}
            onClick={() => handleSort("name")}
          >
            Name{" "}
            <CIcon
              className={`${sortName ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              expire ? "border-bottom border-primary border-3 bg-none" : " "
            } border-0  mx-2  `}
            onClick={() => handleSort("expire")}
          >
            Expire First{" "}
            <CIcon
              className={`${expire ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center"> No </CTableHeaderCell>
              <CTableHeaderCell> Name</CTableHeaderCell>
              <CTableHeaderCell>Gift Card</CTableHeaderCell>
              <CTableHeaderCell>Getting</CTableHeaderCell>
              <CTableHeaderCell>Giving</CTableHeaderCell>
              <CTableHeaderCell>Start Date</CTableHeaderCell>
              <CTableHeaderCell>Expire Date</CTableHeaderCell>
              <CTableHeaderCell>Date Added </CTableHeaderCell>
              <CTableHeaderCell>Added By</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allGiftCardOffers?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={!item.active && "danger"}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    {item?.uid}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-center"> {item.giftCard?.name}</div>
                  </CTableDataCell>

                  <CTableDataCell className="">
                    <div className="text-center">{item.rateGetting}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-center">{item.rateGiving}</div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-center">
                      {dateFormatter(item.startDate)}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-center">
                      {" "}
                      {dateFormatter(item.endDate)}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-center">
                      {" "}
                      {dateFormatter(item.createdAt)}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-center"> {item.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center ">
                    <CTooltip content="edit giftcard">
                      <button
                        type="button"
                        onClick={() =>
                          navigate(
                            `/giftcards/offers/update-offers/${item._id}`
                          )
                        }
                        className="border rounded shadow px-2 py-1 text-primary m-1"
                      >
                        <CIcon icon={cilPen} />
                      </button>
                    </CTooltip>
                    <CTooltip content="delete giftcard">
                      <button
                        type="button"
                        onClick={() => handleDelete(item._id)}
                        className={`${
                          item.disable ? " text-secondary" : "text-danger"
                        } border rounded shadow px-2 py-1 m-1 `}
                      >
                        {" "}
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllOffers;
