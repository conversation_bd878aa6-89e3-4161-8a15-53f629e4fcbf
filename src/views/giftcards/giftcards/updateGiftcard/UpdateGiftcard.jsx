import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>orm,
  <PERSON>orm<PERSON><PERSON><PERSON>,
  CRow,
} from "@coreui/react";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useNavigate, useParams } from "react-router-dom";
import ImageUploader from "src/components/fileManagers/imageUploader";
import "../../giftcard.css";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllCategoriesList,
  fetchAllGiftCardsList,
  fetchAllStoresList,
  fetchGiftCardDetails,
  updateGiftCard,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { cilCloudUpload } from "@coreui/icons";
import SearchAndMultiSelect from "src/components/select/SearchAndMultiSelect";
import InputField from "src/components/inputs/InputField";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Text<PERSON>reaField from "src/components/inputs/TextAreaFiled";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import MultipleSelect from "src/components/stores/MultipleSelect";
import { dateInDDMMYYYYFormat } from "src/helperFunctions/dateFormatter";

const schema = yup
  .object({
    name: yup.string().required("giftcard name is a required field"),
    notes: yup.string().required(" notes is a required field"),
    denominations: yup.string().required(" denominations is a required field"),
    qcId: yup.string().required(" QC id  is a required field"),
    discountGetting: yup
      .number()
      .required("discount getting  is a required field"),
    cashbackGiving: yup
      .number()
      .required(" cashback giving  is a required field"),
    minimumAmount: yup.number().required(" minimum amount is a required field"),
    maximumAmount: yup.number().required(" maximum amount is a required field"),
    validity: yup.date().required("validity is a required field"),
  })
  .required();

function EditGiftCard() {
  const [provider, setProvider] = useState("QUIKCILVER");
  const [logo, setLogo] = useState(null);
  const [image, setImage] = useState(null);
  const [qwickcilverImage, setQwickcilverImage] = useState(null);
  const [categories, setCategories] = useState([]);
  const [relatedGc, setRelatedGc] = useState([]);
  const [howToUse, setHowToUse] = useState("");
  const [terms, setTerms] = useState("");
  const [description, setDescription] = useState("");
  const [relatedCbStore, setRelatedCbStore] = useState([]);
  const [relatedInstantStore, setRelatedInstantStore] = useState([]);
  const [defaultRelatedInstantStore, setDefaultRelatedInstantStore] = useState(
    []
  );
  const [defaultCategories, setDefaultCategories] = useState([]);
  const [defaultGC, setDefaultGC] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { giftcardId } = useParams();
  const { allGiftCardsList, giftCardDetails } = useSelector(
    (state) => state.giftCard
  );
  const { allCategoryList } = useSelector((state) => state.category);
  const { allStoresList } = useSelector((state) => state.store);
  console.log(giftCardDetails);

  useEffect(() => {
    if (giftcardId) {
      dispatch(fetchGiftCardDetails(giftcardId));
    }
    dispatch(fetchAllCategoriesList());
    dispatch(fetchAllStoresList());
    dispatch(fetchAllGiftCardsList({}));
  }, [dispatch, giftcardId]);
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = (data) => {
    if (!image || !logo) {
      return toast.custom(<Danger message={"please upload image or logo"} />);
    }
    if (!howToUse || !terms) {
      return toast.custom(
        <Danger message={"please add how to use and terms and conditions"} />
      );
    }

    const payload = {
      ...data,
      image,
      logo,
      description,
      howToUse,
      terms,
      provider,
      relatedInstantStore,
      relatedCbStore,
      categories,
      relatedGc,
    };
    dispatch(updateGiftCard({ giftcardId, payload }));
  };

  useEffect(() => {
    if (giftCardDetails) {
      reset(
        {
          name: giftCardDetails?.name,
          notes: giftCardDetails?.notes,
          denominations: giftCardDetails?.denominations,
          qwickcilverId: giftCardDetails?.qwickcilverId,
          discountGetting: giftCardDetails?.discountGetting,
          cashbackGiving: giftCardDetails?.discountGetting,
          minimumAmount: giftCardDetails?.minimumAmount,
          maximumAmount: giftCardDetails?.maximumAmount,
          validity: dateInDDMMYYYYFormat(giftCardDetails?.validity),
        },
        { keepDirty: true, keepErrors: true }
      );
      setDefaultCategories(giftCardDetails?.categories);
      setDefaultRelatedInstantStore({
        value: giftCardDetails?.relatedInstantStore?._id,
        label: giftCardDetails?.relatedInstantStore?.name,
      });
      setDefaultGC({
        value: giftCardDetails?.relatedCbStore?._id,
        label: giftCardDetails?.relatedCbStore?.name,
      });
      setRelatedCbStore(giftCardDetails?.relatedCbStore?._id);
      setImage(giftCardDetails?.image);
      setLogo(giftCardDetails?.logo);
      setDescription(giftCardDetails?.description);
      setHowToUse(giftCardDetails?.howToUse);
      setTerms(giftCardDetails?.terms);
      setQwickcilverImage(giftCardDetails?.qwickcilverImage);
    }
  }, [giftCardDetails, reset]);

  const handleSelectStore = (values) => {
    setRelatedCbStore(values?.value);
  };
  const handleSelectInstantStore = (values) => {
    setRelatedInstantStore(values?.value);
  };
  const handleSelectRelatedGiftCards = (values) => {
    setRelatedGc(values.map((item) => item.value));
  };
  return (
    <CContainer fluid>
      <h3 className="text-center">Edit Giftcard</h3>
      <CRow className="justify-content-center ">
        <CCol md={10} lg={10} xl={8} className="rounded shadow">
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <CRow className="my-3 ">
              <InputField
                state={"name"}
                title={"Giftcard Name"}
                type={"text"}
                setState={register}
                error={errors?.name?.message}
              />

              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Provider</p>
              </CCol>
              <CCol md={8} lg={8} xl={8} className="flex">
                <CFormCheck
                  type="radio"
                  name="flexRadio"
                  id="flexRadioDefault1"
                  label="QUIKCILVER"
                  onClick={() => setProvider("QUIKCILVER")}
                  defaultChecked
                />
                <CFormCheck
                  type="radio"
                  name="flexRadio"
                  id="flexRadioDefault2"
                  label="SWYCH"
                  onClick={() => setProvider("SWYCH")}
                />
              </CCol>
              <InputField
                state={"qwickcilverId"}
                title={"QC Id"}
                type={"text"}
                setState={register}
                error={errors?.qwickcilverId?.message}
              />

              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Related Cashback Store</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    defaultValue={defaultGC}
                    array={allStoresList}
                    handleSelectedValue={handleSelectStore}
                    placeholder={"Select Store..."}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Related Instant Stores</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    defaultValue={defaultRelatedInstantStore}
                    array={allStoresList}
                    handleSelectedValue={handleSelectInstantStore}
                    placeholder={"Select Instant Store..."}
                  />
                </CCol>
              </CRow>
              <InputField
                state={"minimumAmount"}
                title={"Minimum Amount"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.minimumAmount?.message}
              />
              <InputField
                state={"maximumAmount"}
                title={"Maximum Amount"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.maximumAmount?.message}
              />
              <InputField
                state={"denominations"}
                title={"Denominations"}
                type={"text"}
                setState={register}
                error={errors?.denominations?.message}
              />
              <InputField
                state={"discountGetting"}
                title={"Discount Getting"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.discountGetting?.message}
              />
              <InputField
                state={"cashbackGiving"}
                title={"Cashback Offer"}
                // type={"number"}
                type="decimal"
                step="0.01"
                setState={register}
                error={errors?.cashbackGiving?.message}
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Gift Card Description</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={description} setData={setDescription} />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Gift Card Logo</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <ImageUploader
                    setFile={setLogo}
                    icon={cilCloudUpload}
                    keyword={"upload giftcard logo"}
                    label={"upload_logo"}
                  />
                  <div className="w-100">
                    {logo && (
                      <img className="w-50 m-2" alt="" src={logo?.secureUrl} />
                    )}
                  </div>
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Gift Card Image</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <ImageUploader
                    setFile={setImage}
                    icon={cilCloudUpload}
                    keyword={"upload giftcard image"}
                    label={"upload_image"}
                  />
                  <div className="w-100">
                    {image && (
                      <img className="w-50 m-2" alt="" src={image?.secureUrl} />
                    )}
                  </div>
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">QC Image Link</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <input
                    type="text"
                    className="border rounded w-100 py-2 mb-1"
                    value={qwickcilverImage?.secureUrl}
                    onChange={(e) =>
                      setQwickcilverImage({
                        publicId: "",
                        secureUrl: e.target.value,
                      })
                    }
                  />
                  <ImageUploader
                    setFile={setQwickcilverImage}
                    icon={cilCloudUpload}
                    keyword={"upload QC image"}
                    label={"upload_qc_image"}
                  />
                  <div className="w-100">
                    {qwickcilverImage && (
                      <img
                        className="w-50 m-2"
                        alt=""
                        src={qwickcilverImage?.secureUrl}
                      />
                    )}
                  </div>
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Categories</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MultipleSelect
                    defaultCategories={defaultCategories}
                    options={allCategoryList}
                    setCategories={setCategories}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Related Gift Cards</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndMultiSelect
                    array={allGiftCardsList}
                    handleSelectedValue={handleSelectRelatedGiftCards}
                    placeholder={"Select Related GiftCards..."}
                  />
                </CCol>
              </CRow>
              <InputField
                state={"validity"}
                title={"Validity"}
                type={"datetime-local"}
                setState={register}
                error={errors?.validity?.message}
                currentDateTime
              />
              <TextAreaField
                state={"notes"}
                title={"Notes"}
                type={"text"}
                setState={register}
                error={errors?.notes?.message}
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Store How To use</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={howToUse} setData={setHowToUse} />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Terms and Conditions</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={terms} setData={setTerms} />
                </CCol>
              </CRow>
              <div className="text-center mt-4 pt-4">
                <CButton
                  type="reset"
                  className="text-light ms-auto mx-1"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  type="submit"
                  className="text-light me-auto mx-1"
                  color="success"
                >
                  Update
                </CButton>
              </div>
            </CRow>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default EditGiftCard;
