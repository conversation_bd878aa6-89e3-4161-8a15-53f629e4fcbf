import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import {
  CButton,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import { cilLockLocked, cilLockUnlocked, cilPencil } from "@coreui/icons";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllTestimonials,
  handleOpenModal,
  updateTestimonialActiveStatus,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import { CSmartPagination } from "@coreui/react-pro";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllTestimonials = () => {
  const navigate = useNavigate();
  // password reset
  const [banner, setBanner] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allTestimonials, page, pages, pageSize, loading } = useSelector(
    (state) => state.testimonials
  );

  useEffect(() => {
    dispatch(fetchAllTestimonials({}));
  }, [dispatch]);

  const handleBlock = async (id) => {
    dispatch(updateTestimonialActiveStatus(id));
  };

  const handleDeleteClick = (data) => {
    setBanner(data);
    setVisible(true);
  };

  const deleteBanner = async () => {};
  const handlePagination = (value) => {
    dispatch(fetchAllTestimonials({ page: value }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="mb-3 text-end ">
          <CButton
            onClick={() => navigate("/testimonials/create")}
            color="primary"
            className=" "
            size="sm"
          >
            Create Testimonial
          </CButton>
        </div>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">No</CTableHeaderCell>
              <CTableHeaderCell>Reviewer </CTableHeaderCell>
              <CTableHeaderCell> Reviewer Name</CTableHeaderCell>
              <CTableHeaderCell> Rating</CTableHeaderCell>
              <CTableHeaderCell> Review</CTableHeaderCell>
              {/* <CTableHeaderCell>CreatedBy</CTableHeaderCell> */}
              <CTableHeaderCell>CreatedAt</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allTestimonials?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  className=" "
                  color={!item.active ? "danger" : ""}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.reviewerAvatar?.secureUrl,
                          })
                        )
                      }
                      width={70}
                      src={item?.reviewerAvatar?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.reviewerName}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.rating}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {" "}
                      <ReadMore text={item.review} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <CTooltip content="edit banner details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/testimonials/edit/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.active ? "block " : "unblock "}>
                      <button
                        type="button"
                        className={`${
                          !item.active ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        onClick={() => handleBlock(item._id)}
                      >
                        {!item.isActive ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Deleting testimonial is irreversible. Proceed with caution."}
        setState={deleteBanner}
        visible={visible}
        setVisible={setVisible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllTestimonials;
