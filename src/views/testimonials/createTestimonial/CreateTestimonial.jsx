import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>orm, CRow } from "@coreui/react";
import React, { useState } from "react";
import CIcon from "@coreui/icons-react";
import { cilCloudUpload, cilTrash } from "@coreui/icons";
import ImageUploader from "src/components/fileManagers/imageUploader";

import { useDispatch } from "react-redux";
import { createTestimonials } from "src/redux/features";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";

const dummyImage =
  "https://i.pinimg.com/474x/07/5e/09/075e09953e21d9ed8d4a143ca2fe991d.jpg";

function CreateTestimonial() {
  const [reviewerAvatar, setReviewerAvatar] = useState();
  const [reviewerName, setReviewerName] = useState("");
  const [rating, setRating] = useState("1");
  const [review, setReview] = useState("");
  const dispatch = useDispatch();

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!reviewerAvatar) {
      return toast.custom(<Danger message={"please update image"} />);
    }
    const payload = {
      review,
      rating,
      reviewerName,
      reviewerAvatar,
    };
    dispatch(createTestimonials(payload));
  };

  return (
    <CContainer fluid>
      <div className=" ">
        <h4 className="text-secondary text-center ">Create Testimonials</h4>
      </div>
      <CForm onSubmit={handleSubmit}>
        <CRow className="p-2 pt-4 rounded shadow ">
          <CCol className=" " md={6}>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Reviewer Name
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="text"
                value={reviewerName}
                onChange={(e) => setReviewerName(e.target.value)}
                required
              />
            </div>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Rating
              </label>
              <select
                className="w-100 p-2 rounded border "
                value={rating}
                onChange={(e) => setRating(e.target.value)}
                required
              >
                <option value={1}>1</option>
                <option value={2}>2</option>
                <option value={3}>3</option>
                <option value={4}>4</option>
                <option value={5}>5</option>
              </select>
            </div>
            <div className="my-3 ">
              <label className="w-100 my-2" htmlFor="">
                Review
              </label>
              <textarea
                name=""
                id=""
                rows="5"
                required
                value={review}
                placeholder="Review"
                className="w-100 border rounded"
                onChange={(e) => setReview(e.target.value)}
              />
            </div>
          </CCol>
          <CCol
            md={6}
            className="d-flex flex-column justify-content-center align-items-center"
          >
            <div className="w-100 mx-auto text-center ">
              {reviewerAvatar && (
                <img
                  style={{ width: "15rem", height: "auto" }}
                  className=" offer-img my-2"
                  src={reviewerAvatar?.secureUrl}
                  alt=""
                />
              )}
            </div>
            <div className="d-flex justify-content-center align-items-center my-2">
              <ImageUploader
                setFile={setReviewerAvatar}
                icon={cilCloudUpload}
                keyword={"Upload Image"}
              />
              <button
                type="button"
                className="btn btn-outline-danger mx-2 "
                onClick={() => setReviewerAvatar(null)}
              >
                <CIcon icon={cilTrash} />
                Remove
              </button>
            </div>
          </CCol>
          <div className="d-flex justify-content-center align-items-center my-4">
            <button className="btn btn-outline-danger mx-2" type="reset">
              Cancel
            </button>
            <button className="btn btn-outline-success mx-2" type="submit">
              Create
            </button>
          </div>
        </CRow>
      </CForm>
    </CContainer>
  );
}

export default CreateTestimonial;

