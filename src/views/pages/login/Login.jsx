import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import { cilLockLocked, cilUser } from "@coreui/icons";
import { useDispatch, useSelector } from "react-redux";
import { loginAdmin } from "src/redux/features/index.jsx";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const schema = yup
  .object({
    email: yup.string().required(),
    password: yup.string().required(),
  })
  .required();
const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { isAuthenticated, loading } = useSelector((state) => state.admin);

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/");
    }
  }, [navigate, isAuthenticated]);

  const onSubmit = (data) => {
    dispatch(loginAdmin(data));
  };

  return (
    <div className="bg-light min-vh-100 d-flex flex-row align-items-center">
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={8} lg={6}>
            <CCardGroup>
              <CCard className="">
                <CCardBody>
                  <CForm onSubmit={handleSubmit(onSubmit)} className="p-4">
                    <h1>Login</h1>
                    <p className="text-medium-emphasis">
                      Sign In to your admin account
                    </p>
                    <CInputGroup className="mb-4 position-relative ">
                      <CInputGroupText>
                        <CIcon icon={cilUser} />
                      </CInputGroupText>
                      <CFormInput
                        placeholder="email"
                        autoComplete="email"
                        className={`${
                          errors?.email && "border-danger"
                        } rounded  mx-1 `}
                        name="email"
                        {...register("email")}
                        type="email"
                        defaultValue={""}
                        required
                      />
                      <p
                        style={{ right: 5, bottom: -15 }}
                        className="position-absolute text-danger"
                      >
                        {errors?.email?.message}
                      </p>
                    </CInputGroup>
                    <CInputGroup className={"mb-4 position-relative "}>
                      <CInputGroupText>
                        <CIcon icon={cilLockLocked} />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        name="password"
                        className={`${
                          errors?.password && "border-danger"
                        } rounded  mx-1 `}
                        placeholder="Password"
                        autoComplete="current-password"
                        {...register("password")}
                        defaultValue={""}
                      />
                      <p
                        style={{ right: 5, bottom: -15 }}
                        className="position-absolute text-danger"
                      >
                        {errors?.password?.message}
                      </p>
                    </CInputGroup>
                    <CRow>
                      <CCol xs={12} className=" ml-auto text-end ">
                        {!loading ? (
                          <CButton
                            color="primary"
                            className="px-4 mx-5"
                            type="submit"
                          >
                            Login
                          </CButton>
                        ) : (
                          <button
                            className="btn btn-sm border rounded mx-5"
                            type="button"
                            disabled
                          >
                            <img
                              className=""
                              width={70}
                              height={32}
                              src="https://miro.medium.com/v2/resize:fit:1400/1*CsJ05WEGfunYMLGfsT2sXA.gif"
                              alt=""
                            />
                          </button>
                        )}
                      </CCol>
                    </CRow>
                  </CForm>
                </CCardBody>
              </CCard>
            </CCardGroup>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
};

export default Login;
