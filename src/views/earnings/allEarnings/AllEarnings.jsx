import { cilPenAlt, cilThumbDown, cilThumbUp, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeader<PERSON>ell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import {
  dateDeferenceFormatter,
  dateFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteEarning,
  fetchAllAffiliationsList,
  fetchAllEarnings,
  fetchAllStoresList,
  trackedToCancelEarning,
  trackedToConfirmEarning,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import ReadMore from "src/components/text/ReadMore";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function PendingEarnings() {
  const [status, setStatus] = useState("all");
  const [store, setStore] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  //const [search, setSearch] = useState("");
  const [affiliation, setAffiliation] = useState("");
  // sort
  const [visible, setVisible] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  const [searchParams, setSearchParams] = useState({
    genrealSearch: "",
    userIdSearch: "",
    orderIdSearch: "",
    advIdSearch: "",
    remarksSearch: "",
  });

  const {
    allEarnings,
    page,
    pages,
    loading,
    totalAmountGotFromPartner,
    totalAmountGotPending,
    totalAmountGotCancelled,
    totalAmountGotConfirmed,
    totalAmountToGiveToUser,
    totalAmountToGivePending,
    totalAmountToGiveCancelled,
    totalAmountToGiveConfirmed,
    statByStore,
  } = useSelector((state) => state.earnings);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
  }, [dispatch]);

  const handlePagination = async (value) => {
    const params = {
      search: searchParams.genrealSearch,
      userId: searchParams.userIdSearch,
      orderId: searchParams.orderIdSearch,
      advId: searchParams.advIdSearch,
      remarks: searchParams.remarksSearch,
      startDate: startDate,
      endDate: endDate,
      status: status?._id,
      store: store?._id,
      affiliation: affiliation?._id,
      page: value,
    };

    updateURLParams(params);
    dispatch(
      fetchAllEarnings({
        //page: value,
        ...params,
      })
    );
  };

  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteId(earningId);
  };

  const handleApproveEarning = async (earningId) => {
    dispatch(trackedToConfirmEarning(earningId));
  };
  const handleCancelEarning = async (earningId) => {
    dispatch(trackedToCancelEarning(earningId));
  };
  const handleDeleteEarnings = async () => {
    dispatch(deleteEarning(deleteId));
  };

  const handleSelectAffiliations = (values) => {
    //setAffiliation(values?.value);
    setAffiliation({
      _id: values?.value,
      label: values?.label,
    });
    //dispatch(fetchAllEarnings({ affiliation: values?.value, store, status }));
  };
  const handleSelectStores = (values) => {
    //setStore(values?.value);
    setStore({
      _id: values?.value,
      label: values?.label,
    });

    //dispatch(fetchAllEarnings({ store: values?.value, affiliation, status }));
  };
  const handleSelectStatus = (values) => {
    setStatus({
      _id: values?.value,
      label: values?.label,
    });
  };

  const handleSearchChange = (field) => (e) => {
    setSearchParams((prev) => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  const handleReset = () => {
    setSearchParams({
      genrealSearch: "",
      userIdSearch: "",
      orderIdSearch: "",
      advIdSearch: "",
      remarksSearch: "",
    });
    setStartDate("");
    setEndDate("");
    setStatus("all");
    setStore("");
    setAffiliation("");

    dispatch(
      fetchAllEarnings({
        page: 1,
      })
    );
    updateURLParams("");
  };

  const handleFilter = async () => {
    //dispatch(fetchAllEarnings({ status: values?.value, affiliation, store }));
    const params = {
      search: searchParams.genrealSearch,
      userId: searchParams.userIdSearch,
      orderId: searchParams.orderIdSearch,
      advId: searchParams.advIdSearch,
      remarks: searchParams.remarksSearch,
      startDate: startDate,
      endDate: endDate,
      status: status?._id,
      store: store?._id,
      affiliation: affiliation?._id,
      page: 1,
    };
    updateURLParams(params);

    dispatch(
      fetchAllEarnings({
        //page: page,
        ...params,
      })
    );
  };

  // Function to update URL parameters
  const updateURLParams = (params) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.set(key, value);
    });
    window.history.replaceState(null, "", `?${searchParams.toString()}`);
  };

  //write a useEffect to read url and set the all params
  useEffect(() => {
    if (allStoresList.length > 0 && allAffiliationsList.length > 0) {
      const urlParams = new URLSearchParams(window.location.search);
      const params = Object.fromEntries(urlParams.entries());
      setSearchParams({
        genrealSearch: params.search,
        userIdSearch: params.userId,
        orderIdSearch: params.orderId,
        advIdSearch: params.advId,
        remarksSearch: params.remarks,
      });

      setStartDate(params.startDate);
      setEndDate(params.endDate);
      const storeItem = allStoresList.find((item) => item._id === params.store);
      const affiliationItem = allAffiliationsList.find(
        (item) => item._id === params.affiliation
      );

      setStatus(
        params.status
          ? {
              _id: params.status,
              label:
                params.status == "confirmed"
                  ? "Only Confirmed"
                  : params.status === "cancelled"
                  ? "Only Cancelled"
                  : params.status === "pending"
                  ? "Only Pending"
                  : params.status === "only_past_confirm_date"
                  ? "Only past  confirm date"
                  : params.status === "tracked_for_confirm"
                  ? "Tracked for Confirm"
                  : params.status === "tracked_for_cancel"
                  ? "Tracked for Cancel"
                  : "All",
            }
          : ""
      );

      setStore(
        storeItem
          ? {
              _id: storeItem._id,
              label: storeItem.name,
            }
          : ""
      );
      setAffiliation(
        affiliationItem
          ? {
              _id: affiliationItem._id,
              label:
                affiliationItem.name ||
                affiliationItem.title ||
                affiliationItem.sectionName,
            }
          : ""
      );
      dispatch(
        fetchAllEarnings({
          status: params.status,
          affiliation: params.affiliation,
          store: params.store,
          search: params.search,
          userId: params.userId,
          orderId: params.orderId,
          advId: params.advId,
          remarks: params.remarks,
          startDate: params.startDate,
          endDate: params.endDate,
          page: params.page,
        })
      );
    }
  }, [allStoresList, allAffiliationsList]);

  // Update URL whenever searchParams change
  //useEffect(() => {
  //  updateURLParams(searchParams);
  //}, [searchParams]);
  //
  //
  //
  // useEffect(() => {
  //   console.log(store, "store")
  //   console.log(affiliation, "affiliation")
  //   console.log(status, "status")
  //   console.log(startDate, "startDate")
  //   console.log(endDate, "endDate")
  //   console.log(searchParams, "searchParams")
  // }, [status]);

  return (
    <CContainer className="">
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />

      <CRow className="border rounded p-2 mt-10 pt-[10px]">
        <CCol sm={12} xl={6}>
          <label htmlFor="">
            Store Name/User Name/ Click Id / Earning Id/ Email:
          </label>
          <div className="">
            <input
              type="text"
              onChange={handleSearchChange("genrealSearch")}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.genrealSearch}
            />
          </div>
        </CCol>

        <CCol md={4}>
          <label htmlFor="">Status</label>
          <SearchAndSelect
            array={[
              {
                name: "Only past  confirm date",
                _id: "only_past_confirm_date",
              },
              { name: "Only Confirmed", _id: "confirmed" },
              { name: "Only Cancelled", _id: "cancelled" },
              { name: "Only Pending", _id: "pending" },
              { name: "Tracked for Confirm", _id: "tracked_for_confirm" },
              { name: "Tracked for Cancel", _id: "tracked_for_cancel" },
            ]}
            defaultValue={status}
            handleSelectedValue={handleSelectStatus}
            placeholder={"Select Status..."}
          />
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">User Id</label>
          <div className="">
            <input
              type="text"
              value={searchParams.userIdSearch}
              onChange={handleSearchChange("userIdSearch")}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
            />
          </div>
        </CCol>

        <CCol md={4}>
          <label htmlFor="">Select Partners</label>
          <SearchAndSelect
            array={allAffiliationsList}
            handleSelectedValue={handleSelectAffiliations}
            placeholder={"Select Partners..."}
            defaultValue={affiliation}
          />
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Order Unique Id</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.orderIdSearch}
              onChange={handleSearchChange("orderIdSearch")}
            />
          </div>
        </CCol>

        <CCol md={4}>
          <label htmlFor="">Select Stores</label>
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStores}
            placeholder={"Select Stores..."}
            defaultValue={store}
          />
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Adv Id</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.advIdSearch}
              onChange={handleSearchChange("advIdSearch")}
            />
          </div>
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Remarks</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.remarksSearch}
              onChange={handleSearchChange("remarksSearch")}
            />
          </div>
        </CCol>
        <CRow className="mt-3">
          <CCol xs={12} className="mb-2">
            <p className="h6"> Date Range :</p>
            <div className="d-flex">
              <div>
                <label htmlFor="">Start Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  onChange={(e) => setStartDate(e.target.value)}
                  placeholder=""
                  id=""
                  value={startDate}
                />
              </div>
              <div>
                <label htmlFor="">End Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  onChange={(e) => setEndDate(e.target.value)}
                  id=""
                  value={endDate}
                />
              </div>
            </div>
          </CCol>
        </CRow>

        <CCol className="mt-3 d-flex" sm={12}>
          <button
            onClick={
              () => handleReset()
              // setRefresh(!refresh)
            }
            type="reset"
            className="btn-danger btn text-light px-5 py-2 me-auto mx-2 btn-sm"
          >
            reset
          </button>
          <button
            onClick={() => handleFilter()}
            type="submit"
            className="btn-primary btn px-5 py-2 mx-2 ms-auto btn-sm "
          >
            filter
          </button>
        </CCol>
      </CRow>

      {(searchParams.genrelSearch ||
        searchParams.userIdSearch ||
        searchParams.orderIdSearch ||
        searchParams.advIdSearch ||
        searchParams.remarksSearch ||
        startDate ||
        endDate ||
        (status && status._id !== "all") ||
        store ||
        affiliation) &&
        statByStore.length > 0 && (
          <div>
            <CRow className="my-2 ">
              <CCol
                className="mt-3 d-flex rounded-lg justify-content-around"
                sm={12}
                xs={12}
              >
                <div className="w-full">
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Total amount got from partner: {totalAmountGotFromPartner}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Pending amount from partner: {totalAmountGotPending}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Confirmed amount from partner: {totalAmountGotConfirmed}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Cancelled amount from partner: {totalAmountGotCancelled}{" "}
                    </div>
                  </CRow>
                </div>

                <div className="w-full">
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Total amount to give to users: {totalAmountToGiveToUser}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Pending to give to users: {totalAmountToGivePending}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Cancelled to give to users: {totalAmountToGiveCancelled}{" "}
                    </div>
                  </CRow>
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Confirmed to give to users: {totalAmountToGiveConfirmed}{" "}
                    </div>
                  </CRow>
                </div>
              </CCol>
            </CRow>

            <CRow className="">
              <CTable align="middle" className=" border" striped hover bordered>
                <CTableHead color="dark">
                  <CTableRow>
                    <CTableHeaderCell>Store</CTableHeaderCell>
                    <CTableHeaderCell>Count</CTableHeaderCell>
                    <CTableHeaderCell>Amount Got</CTableHeaderCell>
                    <CTableHeaderCell>Amount To Give</CTableHeaderCell>
                    <CTableHeaderCell>Sale Amount</CTableHeaderCell>
                    <CTableHeaderCell>AOV</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {!loading &&
                    statByStore?.map((item) => (
                      <CTableRow
                        v-for="item in tableItems"
                        className=" "
                        //color={!item.active ? "danger" : ""}
                        key={item._id}
                      >
                        <CTableDataCell>
                          <div className="text-center">{item?.storeName}</div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">{item?.count}</div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalAmountGot.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalAmountToGive.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalSaleAmount.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>{item?.aov.toFixed(2)}</CTableDataCell>
                      </CTableRow>
                    ))}
                </CTableBody>
              </CTable>

              {loading && <LoadingComponent />}
            </CRow>
          </div>
        )}

      <CRow className="">
        <CTable align="middle" className=" border" striped hover bordered>
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>actions</CTableHeaderCell>
              <CTableHeaderCell className="">ID</CTableHeaderCell>
              <CTableHeaderCell>User Id/User </CTableHeaderCell>
              <CTableHeaderCell>Order Id </CTableHeaderCell>
              <CTableHeaderCell>Click Id </CTableHeaderCell>
              <CTableHeaderCell>Referred By</CTableHeaderCell>
              <CTableHeaderCell>Store Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliation</CTableHeaderCell>
              <CTableHeaderCell>Sale Amount</CTableHeaderCell>
              <CTableHeaderCell>Amount Got</CTableHeaderCell>
              <CTableHeaderCell>Amount To Give</CTableHeaderCell>
              <CTableHeaderCell>Orders Count/ID</CTableHeaderCell>
              <CTableHeaderCell>Added Date</CTableHeaderCell>
              <CTableHeaderCell>Confirm Date</CTableHeaderCell>
              <CTableHeaderCell>Transaction Date</CTableHeaderCell>
              <CTableHeaderCell>Tracking Time</CTableHeaderCell>
              <CTableHeaderCell>Notes</CTableHeaderCell>
              <CTableHeaderCell>Remarks</CTableHeaderCell>
              <CTableHeaderCell>Earnings Type</CTableHeaderCell>
              <CTableHeaderCell>Status</CTableHeaderCell>
              <CTableHeaderCell>Admin</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allEarnings?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  className="mx-2"
                  key={index}
                >
                  <CTableDataCell>
                    <CTooltip content={"Approve for confirmation"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow  px-2 pt-1 text-success m-1"
                        onClick={() => handleApproveEarning(item._id)}
                      >
                        <CIcon icon={cilThumbUp} />
                      </button>
                    </CTooltip>
                    <CTooltip content={"Approve for cancellation"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow m-1 px-2 pt-1 text-warning mx-1"
                        onClick={() => handleCancelEarning(item._id)}
                      >
                        <CIcon icon={cilThumbDown} />
                      </button>
                    </CTooltip>
                    <CTooltip content={"Edit Earnings"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow text-info px-2  m-1 pt-1"
                        onClick={(e) => navigate(`/earnings/edit/${item._id}`)}
                      >
                        {" "}
                        <CIcon icon={cilPenAlt} />
                      </button>
                    </CTooltip>
                    <CTooltip content={"Delete Earnings"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow text-danger px-2  m-1 pt-1"
                        onClick={() => alertDelete(item._id)}
                      >
                        {" "}
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div className="">{item?.uid} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">
                      {item?.user?.uid}/{item?.user?.email}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.orderUniqueId}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.click?.referenceId ?? ""}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.offer?.title}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.affiliation?.uid}/{item?.affiliation?.name}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.saleAmount}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.amountGot}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.cashbackAmount}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.orderCount}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell className="fs-sm">
                    <div>
                      <small>
                        {dateFormatter(item?.confirmDate ?? item.confirmDate)}
                      </small>
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="fs-sm">
                    <div>
                      <small>
                        {dateFormatter(item?.purchaseDate ?? item.createdAt)}
                      </small>
                    </div>
                  </CTableDataCell>

                  <CTableDataCell>
                    <div className="text-center ">
                      {dateDeferenceFormatter(
                        item?.createdAt,
                        item?.trackingTime
                      )}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.notes} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.remarks} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">{item?.earningsType}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">
                      {item?.status?.replace(/_/g, " ")}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.createdBy?.name}</div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete earnings ?"}
        setState={handleDeleteEarnings}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default PendingEarnings;
