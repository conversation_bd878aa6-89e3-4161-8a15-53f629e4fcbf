import {
  cil<PERSON><PERSON><PERSON><PERSON>,
  cil<PERSON><PERSON>,
  cil<PERSON>en,
  cilThumbDown,
  cilThumbUp,
} from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  cancelEarning,
  confirmEarning,
  fetchAllAffiliationsList,
  fetchAllEarnings,
  fetchAllStoresList,
  openReferralCommissionModal,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import { useNavigate, useParams } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function IndividualOrders() {
  const [search, setSearch] = useState("");
  const [visible, setVisible] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { storeId, userId } = useParams();

  const { allEarnings, page, pages, loading } = useSelector(
    (state) => state.earnings
  );

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllEarnings({ store: storeId, user: userId }));
  }, [dispatch]);

  const handlePagination = async (value) => {
    dispatch(fetchAllEarnings({ store: storeId, user: userId, page: value }));
  };
  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteId(earningId);
  };
  const handleDelete = async () => {};

  const handleConfirmEarning = async (earningId) => {
    dispatch(confirmEarning(earningId));
  };
  const handleCancelEarning = async (earningId) => {
    dispatch(cancelEarning(earningId));
  };

  const handleAddReferralCommission = async (earningId, userDetails) => {
    dispatch(
      openReferralCommissionModal({
        earningId,
        userName: userDetails?.name,
        uid: userDetails?.uid,
        userId: userDetails?._id,
      })
    );
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol md={6} className=" ">
          <input
            type="text"
            className=" border rounded py-1 px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell>Actions</CTableHeaderCell>
                <CTableHeaderCell >ID</CTableHeaderCell>
                <CTableHeaderCell>User Id/User </CTableHeaderCell>
                <CTableHeaderCell>Referred By</CTableHeaderCell>
                <CTableHeaderCell>Store Name</CTableHeaderCell>
                <CTableHeaderCell>Affiliation</CTableHeaderCell>
                <CTableHeaderCell>Sale Amount</CTableHeaderCell>
                <CTableHeaderCell>Amount Got</CTableHeaderCell>
                <CTableHeaderCell>Amount To Give</CTableHeaderCell>
                <CTableHeaderCell>Orders Count/ID</CTableHeaderCell>
                <CTableHeaderCell>Added Date</CTableHeaderCell>
                <CTableHeaderCell>Transaction Date</CTableHeaderCell>
                <CTableHeaderCell>Notes</CTableHeaderCell>
                <CTableHeaderCell>Remarks</CTableHeaderCell>
                <CTableHeaderCell>Status</CTableHeaderCell>
                <CTableHeaderCell>Admin</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allEarnings?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" key={index}>
                     <CTableDataCell>
                      <CTooltip content={"confirm the earning"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow  px-2 pt-1 text-success m-1"
                          onClick={() => handleConfirmEarning(item._id)}
                        >
                          <CIcon icon={cilThumbUp} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"cancel the earning"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow m-1 px-2 pt-1 text-danger mx-1"
                          onClick={() => handleCancelEarning(item._id)}
                        >
                          <CIcon icon={cilThumbDown} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"edit earnings"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow m-1 px-2 pt-1 text-warning mx-1"
                          onClick={() => navigate(`/earnings/edit/${item._id}`)}
                        >
                          <CIcon icon={cilPen} />
                        </button>
                      </CTooltip>
                      {item?.user?.referral?._id && (
                        <CTooltip content={"add referral commission"}>
                          <button
                            type="button"
                            style={{ height: "2rem", fontSize: "12px" }}
                            className={`border rounded shadow  text-primary px-2 pt-1 m-1 ${
                              item?.mergedCount == 1 ? " d-one" : ""
                            } `}
                            onClick={() =>
                              handleAddReferralCommission(
                                item?._id,
                                item?.user?.referral
                              )
                            }
                          >
                            <CIcon icon={cilMoney} />
                          </button>
                        </CTooltip>
                      )}
                      <CTooltip content={"user statistics"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow text-info px-2  m-1 pt-1"
                        >
                          {" "}
                          <CIcon icon={cilBarChart} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">{item?.uid} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="">
                        {item?.user?.uid}/{item?.user?.email}{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.user?.referral?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.store?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      {item?.affiliation && (
                        <div>
                          {item?.affiliation?.uid} / {item?.affiliation?.name}
                        </div>
                      )}
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.saleAmount}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.amountGot}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.cashbackAmount}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>
                        {item?.orderCount}/{item?.orderUniqueId}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{dateFormatter(item?.click?.createdAt)}</div>
                    </CTableDataCell>
                    <CTableDataCell className="fs-sm">
                      <div>
                        <small>{dateFormatter(item?.createdAt)}</small>
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        <ReadMore text={item?.notes} />{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        <ReadMore text={item?.remarks} />{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="text-capitalize">
                        {item?.status.replace(/_/g, " ")}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.createdBy?.name}</div>
                    </CTableDataCell>
                   
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete ?"}
        setState={handleDelete}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default IndividualOrders;
