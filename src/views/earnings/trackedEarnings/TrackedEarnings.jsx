import { cilBar<PERSON>hart, cilInfo, cilThumbDown, cilThumbUp } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  cancelEarning,
  confirmEarning,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  fetchAllTrackedEarnings,
} from "src/redux/features";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function TrackedEarnings() {
  const [visible, setVisible] = useState(false);
  const [deleteIds, setDeleteIds] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { page, pages, allTrackedEarnings, loading } = useSelector(
    (state) => state.earnings
  );

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllTrackedEarnings());
  }, [dispatch]);

  const handlePagination = async (value) => {
    dispatch(fetchAllTrackedEarnings({ page: value }));
  };

  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteIds(earningId);
  };
  const handleDelete = async () => {};

  const handleConfirmEarnings = (earningId) => {
    console.log(earningId, "earning ids");
    dispatch(confirmEarning(earningId));
  };

  const handleCancelEarnings = (earningId) => {
    dispatch(cancelEarning(earningId));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="">ID</CTableHeaderCell>
                <CTableHeaderCell>User Id/User </CTableHeaderCell>
                <CTableHeaderCell>Referred By</CTableHeaderCell>
                <CTableHeaderCell>Store And Earnings Details</CTableHeaderCell>
                <CTableHeaderCell>actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allTrackedEarnings?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" key={index}>
                    <CTableDataCell className="text-center">
                      <div className="">{index} </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">
                        {item?.user?.uid}/{item?.user?.name}{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">{item.user.uid} </div>
                    </CTableDataCell>
                    <CTable
                      align="middle"
                      className=" border "
                      striped
                      hover
                      bordered
                      responsive
                    >
                      <CTableHead color="dark">
                        <CTableRow>
                          <CTableHeaderCell>storeId</CTableHeaderCell>
                          <CTableHeaderCell>Store Name </CTableHeaderCell>
                          <CTableHeaderCell>Total SaleAmount</CTableHeaderCell>
                          <CTableHeaderCell>Total AmountGot</CTableHeaderCell>
                          <CTableHeaderCell>
                            Total CashbackAmount
                          </CTableHeaderCell>
                          <CTableHeaderCell>
                            Total Earnings count
                          </CTableHeaderCell>
                          <CTableHeaderCell>Status</CTableHeaderCell>
                          <CTableHeaderCell>Actions</CTableHeaderCell>
                        </CTableRow>
                      </CTableHead>
                      <CTableBody>
                        {item?.stores?.map((storeItem) => (
                          <CTableRow
                            className="border "
                            v-for="item in tableItems "
                            key={storeItem.store}
                          >
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem.storeId}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem.storeName}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.saleAmount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.amountGot}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.cashbackAmount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.earningsCount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.status}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell className=" d-flex">
                              <CTooltip content={"Approve All Orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-success px-2 pt-1 m-1 `}
                                  onClick={() =>
                                    handleConfirmEarnings(
                                      storeItem?.earningsDetails?.earningIds[0]
                                    )
                                  }
                                  disabled={
                                    storeItem?.earningsDetails
                                      ?.earningsCount !== 1
                                  }
                                >
                                  <CIcon icon={cilThumbUp} />
                                </button>
                              </CTooltip>
                              <CTooltip content={"Reject All Orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-danger px-2 pt-1 m-1 `}
                                  onClick={() =>
                                    handleCancelEarnings(
                                      storeItem?.earningsDetails?.earningIds[0]
                                    )
                                  }
                                  disabled={
                                    storeItem?.earningsDetails
                                      ?.earningsCount !== 1
                                  }
                                >
                                  <CIcon icon={cilThumbDown} />
                                </button>
                              </CTooltip>
                              <CTooltip content={"view individual orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-primary px-2 pt-1 m-1 `}
                                  onClick={() =>
                                    navigate(
                                      `/earnings/individual-earnings/${storeItem?.store}/${item?.user?._id}`
                                    )
                                  }
                                >
                                  <CIcon icon={cilInfo} />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          </CTableRow>
                        ))}{" "}
                      </CTableBody>
                    </CTable>
                    <CTableDataCell className="text-center">
                      <CTooltip content={"user statistics"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow text-info px-2  m-1 pt-1"
                        >
                          <CIcon icon={cilBarChart} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete ?"}
        setState={handleDelete}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default TrackedEarnings;
