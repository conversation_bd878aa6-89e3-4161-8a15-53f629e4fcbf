import React, { useEffect, useState } from "react";
import {
  <PERSON>utton,
  CCol,
  <PERSON>ontainer,
  CForm,
  CFormSelect,
  CRow,
  CFormSwitch,
} from "@coreui/react";
import "./store.css";
import MyCkEditor from "src/components/CkEditor/Editor";
import ImageUploader from "src/components/fileManagers/imageUploader";
import Danger from "src/components/alerts/Danger/Danger";
import Warning from "src/components/alerts/Warning/Warning";
import { toast } from "react-hot-toast";
import Inputs from "src/components/stores/Inputs";
import RadioButtons from "src/components/stores/RadioButtons";
import TextAreas from "src/components/stores/TextAreas";
import MultipleSelect from "src/components/stores/MultipleSelect";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAffiliationsList,
  fetchAllCategoriesList,
  fetchAllStoresList,
  fetchStoreDetails,
  updateStore,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { cilCloudUpload } from "@coreui/icons";
import SearchAndMultiSelect from "src/components/select/SearchAndMultiSelect";
import { useNavigate, useParams } from "react-router-dom";
import { removePTagsFromStartAndEnd } from "src/helperFunctions/removeTags";

const termsValue = `<ul>
    <li>Offer valid only till stock ends.</li>
    <li>You will get cashback only if you purchased from the automatically opened merchant’s website by <a href="https://indiancashback.com">indiancashback.com</a></li>
    <li>Restrictions may apply in some cases.</li>
    <li> Cashback is not applicable if the sale is cancelled or if the goods are returned.</li>
    <li>Cashback is often paid excluding VAT, delivery and other administrative charges.</li>
    <li>The usage of coupon will reflect in cashback amount at the time of pending cashback amount raises to approved state.</li>
    <li>You can raise a missing cashback ticket for your transaction within 10 days of the purchase date.</li>
    <li>Any missing cashback ticket after 10 days will not be accepted by the merchant site.</li>
    <li>Some stores do not accept missing cashback. Please check store details for special instructions like that.</li>
    <li>If you are using any ad-blocker software or browser extensions, you may not receive cashback.</li>
</ul>`;
const howToGetValue = `
<ul>
<li>Join indiancashback.com, free, easy- and log in to your account.</li>
<li>Click on the offer you want, this will redirect you to merchant’s website. </li>
<li>  Shop normally there, your cashback will reach your account.</li>
</ul>
`;

function CreateStores() {
  const [name, setName] = useState("");
  const [logo, setLogo] = useState("");
  const [banner, setBanner] = useState("");
  const [categories, setCategories] = useState([]);
  const [offerWarning, setOfferWarning] = useState(""); // str spl warning
  const [affiliation, setAffiliation] = useState("");
  const [selectedAffiliation, setSelectedAffiliation] = useState("");
  const [affiliateLink, setAffiliateLink] = useState(""); // str link
  const [campaignType, setCampaignType] = useState("");
  const [storeOffer, setStoreOffer] = useState("");
  const [relatedStores, setRelatedStores] = useState([]);
  const [minimumAmount, setMinimumAmount] = useState(0);
  const [maximumAmount, setMaximumAmount] = useState(0);
  const [description, setDescription] = useState("");
  const [detailedDescription, setDetailedDescription] = useState(""); // new filed detailed description
  const [reliability, setReliability] = useState("");
  const [trackable, setTrackable] = useState(true);
  const [priority, setPriority] = useState("");
  const [autoCheck, setAutoCheck] = useState(true); // add new radio button
  const [isSpecial, setIsSpecial] = useState(""); // new select field
  const [storeWarning, setStoreWarning] = useState(""); //  popup warning
  const [topWarningLink, setTopWarningLink] = useState("");
  const [storeTopWarning, setStoreTopWarning] = useState(""); //
  const [topWarningShowInactive, setTopWarningShowInactive] = useState(""); //
  const [warningType, setWarningType] = useState(""); // select field web or mobile
  const [storeHowToGet, setStoreHowToGet] = useState(howToGetValue); // like terms and conditions
  const [storeTerms, setStoreTerms] = useState(termsValue);
  const [noAppSaleCategory, setNoAppSaleCategory] = useState(false);
  const [cashbackAmount, setCashbackAmount] = useState("");
  const [cashbackPercent, setCashbackPercent] = useState("");
  const [noMobileWebSaleCategory, setNoMobileWebSaleCategory] = useState(false);
  const [noDesktopWebSaleCategory, setNoDesktopWebSaleCategory] =
    useState(false);
  const [deepLinkEnable, setDeepLinkEnable] = useState(false);
  const [active, setActive] = useState(false);
  const [trackingTime, setTrackingTime] = useState("");
  const [confirmationTime, setConfirmationTime] = useState("30-90 Days");
  const [missingAccepted, setMissingAccepted] = useState(false);
  const [bgColor, setBgColor] = useState("#70367c");
  const [offerType, setOfferType] = useState("");
  const [pointsType, setPointsType] = useState("");
  const { storeId } = useParams();

  const navigate = useNavigate();

  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList, storeDetails } = useSelector((state) => state.store);
  const { allCategoryList } = useSelector((state) => state.category);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList());
    dispatch(fetchAllStoresList());
    dispatch(fetchAllCategoriesList());
    if (storeId) {
      dispatch(fetchStoreDetails(storeId));
    }
  }, [dispatch, storeId]);

  useEffect(() => {
    if (storeDetails) {
      setName(storeDetails?.name);
      setDetailedDescription(storeDetails?.detailedDescription);
      setDescription(storeDetails?.description);
      setLogo(storeDetails?.logo);
      setBanner(storeDetails?.banner);
      setMinimumAmount(storeDetails?.minimumAmount);
      setMaximumAmount(storeDetails?.maximumAmount);
      setAffiliation(storeDetails?.affiliation?._id);
      setSelectedAffiliation({
        value: storeDetails?.affiliation?._id,
        label: storeDetails?.affiliation?.name,
      });
      setPointsType(storeDetails?.cashbackType);
      setCampaignType(storeDetails?.campaignType);
      setStoreOffer(storeDetails?.storeOffer);
      setReliability(storeDetails?.reliability);
      setPriority(storeDetails?.priority);
      setTopWarningLink(storeDetails?.topWarningLink);
      setRelatedStores(
        (storeDetails?.relatedStores &&
          storeDetails?.relatedStores.length > 0 &&
          storeDetails?.relatedStores?.map((item) => ({
            label: item.name,
            value: item._id,
          }))) ||
          []
      );
      setTrackable(storeDetails?.trackable);
      setCategories(storeDetails?.categories);
      setNoAppSaleCategory(storeDetails?.noAppSaleCategory);
      setNoMobileWebSaleCategory(storeDetails?.noMobileWebSaleCategory);
      setNoDesktopWebSaleCategory(storeDetails?.noDesktopWebSaleCategory);
      setDeepLinkEnable(storeDetails?.deepLinkEnable);
      setOfferType(storeDetails?.offerType);
      setActive(storeDetails?.active);
      setConfirmationTime(storeDetails?.confirmationTime);
      setTrackingTime(storeDetails?.trackingTime);
      setMissingAccepted(storeDetails?.missingAccepted === true || storeDetails?.missingAccepted === "yes");
      setOfferWarning(storeDetails?.offerWarning);
      setAffiliateLink(storeDetails?.affiliateLink);
      setAutoCheck(storeDetails?.autoCheck);
      setIsSpecial(storeDetails?.isSpecial);
      setStoreWarning(storeDetails?.storeWarning);
      setStoreTopWarning(storeDetails?.storeTopWarning);
      setTopWarningShowInactive(storeDetails?.topWarningShowInactive);
      setWarningType(storeDetails?.warningType);
      setStoreTerms(storeDetails?.storeTerms);
      setBgColor(storeDetails?.bgColor);
      setCashbackAmount(storeDetails?.cashbackAmount);
      setCashbackPercent(storeDetails?.cashbackPercent);
    }
  }, [storeDetails]);

  const handleSubmit = async (event) => {
    try {
      event.preventDefault();
      const payload = {
        name,
        logo,
        banner,
        noAppSaleCategory,
        noMobileWebSaleCategory,
        noDesktopWebSaleCategory,
        affiliateLink,
        minimumAmount,
        maximumAmount,
        description: removePTagsFromStartAndEnd(description),
        detailedDescription: removePTagsFromStartAndEnd(detailedDescription),
        affiliation,
        campaignType,
        storeOffer,
        reliability,
        priority,
        topWarningLink,
        relatedStores:
          (relatedStores &&
            relatedStores?.length > 0 &&
            relatedStores?.map((item) => item.value)) ||
          [],
        trackable,
        categories,
        offerWarning: removePTagsFromStartAndEnd(offerWarning),
        autoCheck,
        isSpecial,
        storeWarning,
        storeTopWarning,
        topWarningShowInactive,
        warningType,
        storeHowToGet: removePTagsFromStartAndEnd(storeHowToGet),
        storeTerms: removePTagsFromStartAndEnd(storeTerms),

        deepLinkEnable,
        active,
        missingAccepted,
        confirmationTime,
        trackingTime,
        bgColor,
        ...(offerType && { offerType }),
        cashbackType: pointsType,
        cashbackAmount,
        cashbackPercent,
      };

      const data = await dispatch(updateStore({ storeId, payload })).unwrap();
      if (data?.success) {
        navigate("/store/all-stores/null");
      }
    } catch (error) {
      if (error?.response?.status === 403) {
        return toast.custom(<Warning message={"permission denied!"} />);
      }
      toast.custom(
        <Danger message={error?.response?.data?.errors || error.message} />
      );
    }
  };

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectCampaignType = (values) => {
    setCampaignType(values?.value);
  };

  const handleSelectOfferType = (values) => {
    setOfferType(values?.value);
    if (cashbackPercent && values) {
      setStoreOffer(
        `${values.label}  ${cashbackPercent}% ${
          pointsType == "reward" ? "Reward" : "Cashback"
        }`
      );
    }
    if (cashbackAmount && values) {
      setStoreOffer(
        `${values.label} Rs.${cashbackAmount} ${
          pointsType == "reward" ? "Reward" : "Cashback"
        }`
      );
    }
  };
  const handleSelectIsSpecial = (values) => {
    setIsSpecial(values?.value);
  };

  const handleSelectRelatedStores = (values) => {
    // console.log(values, "value");
    setRelatedStores(values);
  };
  const handleChangeOfferText = (values) => {
    if (cashbackPercent && values) {
      setStoreOffer(
        `${offerType == "upto" ? "Up to" : "Flat"} ${cashbackPercent}% ${
          values?.label
        }`
      );
    }
    if (cashbackAmount && values) {
      setStoreOffer(
        `${offerType == "upto" ? "Up to" : "Flat"} Rs.${cashbackAmount} ${
          values?.label
        }`
      );
    }
    setPointsType(values?.value);
  };

  const handleOfferAmount = (value, type) => {
    if (type === "percent") {
      setCashbackAmount("");
      setCashbackPercent(value);
      setStoreOffer(
        `${pointsType == "upto" ? "Up to" : "Flat"} ${value}% ${
          offerType == "reward" ? "Reward" : "Cashback"
        }`
      );
    } else {
      setCashbackAmount(value);
      setStoreOffer(
        `${pointsType == "upto" ? "Up to" : "Flat"} Rs.${value} ${
          offerType == "reward" ? "Reward" : "Cashback"
        }`
      );
      setCashbackPercent("");
    }
  };

  return (
    <CContainer fluid>
      <h3 className="text-center"> Edit Store</h3>
      <CRow className="justify-content-center">
        <CCol md={12} lg={9} xl={9}>
          <CForm onSubmit={handleSubmit}>
            <Inputs
              state={name}
              setState={setName}
              title={"Store Name"}
              type={"text"}
            />
            <RadioButtons
              title={"Deep Link Enabled"}
              name={"flexRadio"}
              state={deepLinkEnable}
              setState={setDeepLinkEnable}
            />

            <RadioButtons
              title={"Active Store"}
              name={"flexRadioStatus"}
              state={active}
              setState={setActive}
            />

            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">BackGround Color</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  style={{ width: 100, height: 50 }}
                  required
                  type="color"
                  value={bgColor}
                  onChange={(e) => setBgColor(e.target.value)}
                />
              </CCol>
            </CRow>
            <RadioButtons
              title={"App Sale Trackable"}
              name={"flexRadioDef"}
              state={noAppSaleCategory}
              setState={setNoAppSaleCategory}
            />
            <RadioButtons
              title={"Mobile Web Sale Trackable"}
              name={"flexRadioDfaul"}
              state={noMobileWebSaleCategory}
              setState={setNoMobileWebSaleCategory}
            />
            <RadioButtons
              title={"Desktop Site Sale Trackable"}
              name={"flexRadiox"}
              state={noDesktopWebSaleCategory}
              setState={setNoDesktopWebSaleCategory}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">affiliation</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  id="affiliationSelection"
                  array={allAffiliationsList}
                  handleSelectedValue={handleSelectAffiliations}
                  placeholder={"Select Partners..."}
                  defaultValue={selectedAffiliation}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Campaign Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  id="campaignTypeSelection"
                  defaultValue={{
                    label: campaignType,
                    value: campaignType,
                  }}
                  array={[
                    { name: "CPS", _id: "CPS" },
                    { name: "CPV", _id: "CPV" },
                    { name: "CPA", _id: "CPA" },
                  ]}
                  handleSelectedValue={handleSelectCampaignType}
                  placeholder={"Select Campaign type..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Offer Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  key={"ddfgdf"}
                  array={[
                    { name: "Up to", _id: "upto" },
                    { name: "Flat", _id: "flat" },
                  ]}
                  defaultValue={
                    offerType == "upto"
                      ? { label: "Up to", value: "upto" }
                      : { label: "Flat", value: "flat" }
                  }
                  handleSelectedValue={handleSelectOfferType}
                  placeholder={"Select Offer type..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={
                    pointsType == "reward"
                      ? { label: "Reward", value: "reward" }
                      : { label: "Cashback", value: "cashback" }
                  }
                  handleSelectedValue={handleChangeOfferText}
                  array={[
                    { name: "Reward", _id: "reward" },
                    { name: "Cashback", _id: "cashback" },
                  ]}
                  placeholder={"Select Cashback type..."}
                />
              </CCol>
            </CRow>
            <Inputs
              state={affiliateLink}
              setState={setAffiliateLink}
              title={"Store Link"}
              type={"text"}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Amount</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="text"
                  title="number only"
                  className="w-100 p-2 border rounded"
                  value={cashbackAmount}
                  onChange={(e) => handleOfferAmount(e.target.value, "amount")}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Percent</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="text"
                  title="number only"
                  className="w-100 p-2 border rounded"
                  value={cashbackPercent}
                  onChange={(e) => handleOfferAmount(e.target.value, "percent")}
                />
              </CCol>
            </CRow>
            <Inputs
              state={storeOffer}
              setState={setStoreOffer}
              title={"store Offer"}
              type={"text"}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Store DesCription</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  content={description}
                  setData={setDescription}
                  toolbar={["bold", "bulletedList"]}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Store Detailed DesCription</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  content={detailedDescription}
                  setData={setDetailedDescription}
                  toolbar={["bold", "bulletedList"]}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Store Special Warning to Show</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  toolbar={["bulletedList"]}
                  content={offerWarning}
                  setData={setOfferWarning}
                />
              </CCol>
            </CRow>
            <Inputs
              state={priority}
              setState={setPriority}
              title={"Store Priority"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">store logo</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <ImageUploader
                  setFile={setLogo}
                  icon={cilCloudUpload}
                  keyword={"Upload store logo"}
                  label={"upload_logo"}
                />
                <div className="w-100">
                  {logo && (
                    <img
                      width={100}
                      className="shadow m-2"
                      src={logo?.secureUrl}
                      alt=""
                    />
                  )}
                </div>
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Banner</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <ImageUploader
                  setFile={setBanner}
                  icon={cilCloudUpload}
                  keyword={"Upload store banner"}
                  label={"upload_banner"}
                />
                <div className="w-100">
                  {banner && (
                    <img
                      width={100}
                      className="shadow m-2"
                      src={banner?.secureUrl}
                      alt=""
                    />
                  )}
                </div>
              </CCol>
            </CRow>
            <RadioButtons
              title={"Auto Check"}
              name={"flexRadi"}
              state={autoCheck}
              setState={setAutoCheck}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Is Special ?</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={{
                    label:
                      isSpecial &&
                      isSpecial.charAt(0).toUpperCase() + isSpecial.slice(1),
                    value: isSpecial,
                  }}
                  array={[
                    { name: "None", _id: "none" },
                    { name: "Special", _id: "special" },
                    { name: "New", _id: "new" },
                    { name: "High", _id: "high" },
                    { name: "100%", _id: "100%" },
                  ]}
                  handleSelectedValue={handleSelectIsSpecial}
                  placeholder={"Select Special..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Categories</p>
              </CCol>
              <CCol className="border" md={9} lg={9} xl={9}>
                <MultipleSelect
                  defaultCategories={storeDetails?.categories}
                  options={allCategoryList}
                  setCategories={setCategories}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Related Stores</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndMultiSelect
                  array={allStoresList}
                  handleSelectedValue={handleSelectRelatedStores}
                  placeholder={"Select Related Stores"}
                  defaultValue={relatedStores}
                />
              </CCol>
            </CRow>
            <Inputs
              state={minimumAmount}
              setState={setMinimumAmount}
              title={"Minimum Amount"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <Inputs
              state={maximumAmount}
              setState={setMaximumAmount}
              title={"Maximum Amount"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <RadioButtons
              title={"Trackable"}
              name={"flexRadioDefault"}
              state={trackable}
              setState={setTrackable}
            />
            <Inputs
              state={reliability}
              setState={setReliability}
              title={"Reliability"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Tracking Time</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormSelect
                  value={trackingTime}
                  onChange={(e) => setTrackingTime(e.target.value)}
                >
                  <option value={"instant"}>Instant</option>
                  <option value={"15 minutes"}>15 minutes</option>
                  <option value={"1-3 days"}>1-3 days</option>
                  <option value={"3-6 days"}>3-6 days</option>
                </CFormSelect>
              </CCol>
            </CRow>
            <Inputs
              state={confirmationTime}
              setState={setConfirmationTime}
              title={"Confirmation Time"}
              type={"text"}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Missing Accepted</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormSwitch
                  id="missingAcceptedSwitch"
                  checked={missingAccepted}
                  onChange={(e) => setMissingAccepted(e.target.checked)}
                />
              </CCol>
            </CRow>
            {/* 
            <TextAreas
              title={"Popup Warning"}
              state={storeWarning}
              setState={setStoreWarning}
            /> */}

            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Popup Warning</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  content={storeWarning}
                  setData={setStoreWarning}
                  toolbar={["bold", "bulletedList"]}
                />
              </CCol>
            </CRow>
            <TextAreas
              title={"Top Warning "}
              state={storeTopWarning}
              setState={setStoreTopWarning}
            />
            <TextAreas
              title={"Top Warning Link"}
              state={topWarningLink}
              setState={setTopWarningLink}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Warning Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormSelect onChange={(e) => setWarningType(e.target.value)}>
                  <option value={"all"}> All</option>
                  <option value={"web only"}>Web Only</option>
                  <option value={"mobile only"}>Mobile Only</option>
                </CFormSelect>
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Store How To Get</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  content={storeHowToGet}
                  setData={setStoreHowToGet}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Terms and Conditions</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor content={storeTerms} setData={setStoreTerms} />
              </CCol>
            </CRow>
            <div className="text-center mt-4 pt-4">
              <CButton
                type="reset"
                onClick={() => navigate("/store/all-stores")}
                className="text-light ms-auto mx-1"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                type="submit"
                className="text-light me-auto mx-1"
                color="success"
              >
                Update
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}
export default CreateStores;
