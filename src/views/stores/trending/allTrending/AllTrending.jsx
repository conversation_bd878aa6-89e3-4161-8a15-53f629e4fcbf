import { cilReportSlash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import {
  deleteTrendingStore,
  fetchAllTrendingStores,
  updateTrendingStorePriority,
} from "src/redux/features";

function AllTrending() {
  const [search, setSearch] = useState("");
  const [visible, setVisible] = useState(false);
  const [trendingDetails, setTrendingDetails] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { allTrendingStores, page, pages, loading } = useSelector(
    (state) => state.trendingStore
  );

  useEffect(() => {
    dispatch(fetchAllTrendingStores({ search }));
  }, [dispatch, search]);

  const handlePagination = async (value) => {
    dispatch(fetchAllTrendingStores({ page: value }));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllTrendingStores({ search: value }));
  };
  const handleDelete = async () => {
    dispatch(deleteTrendingStore(trendingDetails?.trendingId));
  };

  const handleChangePriority = async (storeId, priority) => {
    dispatch(updateTrendingStorePriority({ storeId, priority }));
  };
  const handleDeleteClick = (trendingId, storeName) => {
    setTrendingDetails({ trendingId, storeName });
    setVisible(true);
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol
          xs={12}
          sm={6}
          md={4}
          className="d-flex  justify-content-start align-items-center "
        >
          <input
            type="text"
            className="w-100 border rounded py-2 shadow px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className="d-flex justify-content-end  ms-auto   " sm={8}></CCol>
      </CRow>
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="">NO</CTableHeaderCell>
                <CTableHeaderCell>Store</CTableHeaderCell>
                <CTableHeaderCell>Created At</CTableHeaderCell>
                <CTableHeaderCell>Created By</CTableHeaderCell>
                <CTableHeaderCell>Priority</CTableHeaderCell>
                <CTableHeaderCell>Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allTrendingStores?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" key={index}>
                    <CTableDataCell>
                      <div>{item?.uid}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item.store?.name} </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        {dateFormatter(item?.createdAt)}{" "}
                        <small className="text-info">
                          {timeFormatter(item?.createdAt)}{" "}
                        </small>{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item.createdBy?.name} </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center ">
                      <input
                        type="number"
                        width={30}
                        style={{ width: 50 }}
                        value={item.priority}
                        onChange={(e) =>
                          handleChangePriority(item._id, e.target.value)
                        }
                        className="border rounded "
                      />
                    </CTableDataCell>
                    <CTableDataCell>
                      <CTooltip content="remove from trending">
                        <button
                          onClick={() =>
                            handleDeleteClick(item._id, item.storeId[0]?.name)
                          }
                          className="border rounded shadow px-2 py-1 text-danger"
                        >
                          <CIcon icon={cilReportSlash} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <DeleteAlert
        message={`Are you sure to want to delete '${trendingDetails?.storeName}' store from trending list ?`}
        setState={handleDelete}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllTrending;
