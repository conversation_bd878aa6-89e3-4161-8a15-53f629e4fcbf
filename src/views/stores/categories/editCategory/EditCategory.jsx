import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import Warning from "src/components/alerts/Warning/Warning";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAffiliationsList,
  fetchAllStoresList,
  fetchStoreCategoryDetails,
  fetchStoreDetails,
  updateStoreCategory,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import TextAreaField from "src/components/inputs/TextAreaFiled";
import InputField from "src/components/inputs/InputField";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const schema = yup
  .object({
    gettingOldUserRate: yup
      .number()
      .typeError("Please enter a valid number")
      .min(0, "Value must be greater than or equal to 0")
      // .max(100, "Value must be less than or equal to 100")
      .required("Please enter old user getting"),
    gettingNewUserRate: yup
      .number()
      .typeError("Please enter a valid number")
      .min(0, "Value must be greater than or equal to 0")
      // .max(100, "Value must be less than or equal to 100")
      .required("Please enter new user getting"),
    sectionLink: yup.string().required("please enter a section link"),
    name: yup.string().required("please enter a name"),
    description: yup.string().required("please enter category description"),
  })
  .required();
function EditStoreCategory() {
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const [notes, setNotes] = useState("");
  const [device, setDevice] = useState("website");
  const [userGettingType, setUserGettingType] = useState("percent");
  const [gettingType, setGettingType] = useState("percent");
  const [newUserOfferAmount, setNewUserOfferAmount] = useState(null);
  const [oldUserOfferAmount, setOldUserOfferAmount] = useState(null);
  const [newUserOfferPercent, setNewUserOfferPercent] = useState(null);
  const [oldUserOfferPercent, setOldUserOfferPercent] = useState(null);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);
  const { storeCategoryDetails } = useSelector((state) => state.storeCategory);
  const { categoryId } = useParams();

  useEffect(() => {
    dispatch(fetchAllAffiliationsList());
    dispatch(fetchAllStoresList());
    if (categoryId) {
      dispatch(fetchStoreCategoryDetails(categoryId));
    }
  }, [dispatch, categoryId]);

  const {
    register,
    reset,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (storeCategoryDetails?._id) {
      reset(
        {
          gettingNewUserRate: storeCategoryDetails?.gettingNewUserRate,
          gettingOldUserRate: storeCategoryDetails?.gettingOldUserRate,
          sectionLink: storeCategoryDetails?.sectionLink,
          name: storeCategoryDetails?.name,
          description: storeCategoryDetails?.description,
        },
        { keepDirty: true, keepErrors: true }
      );
      setNotes(storeCategoryDetails?.notes);

      setStore({
        label: storeCategoryDetails?.store?.name,
        value: storeCategoryDetails?.store?._id,
      });
      setAffiliation({
        label: storeCategoryDetails?.affiliation?.name,
        value: storeCategoryDetails?.affiliation?._id,
      });
      setDevice(storeCategoryDetails?.device);
      setOldUserOfferAmount(storeCategoryDetails?.oldUserOfferAmount);
      setOldUserOfferPercent(storeCategoryDetails?.oldUserOfferPercent);
      setNewUserOfferAmount(storeCategoryDetails?.newUserOfferAmount);
      setNewUserOfferPercent(storeCategoryDetails?.newUserOfferPercent);
      setGettingType(storeCategoryDetails?.gettingType);
      setUserGettingType(storeCategoryDetails?.givingType ?? "percent");
    }
  }, [storeCategoryDetails, reset]);

  const onSubmit = async (data) => {
    if (!affiliation) {
      return toast.custom(<Warning message={"please select affiliation"} />);
    }
    if (!store) {
      return toast.custom(<Warning message={"please select category store"} />);
    }
    if (!notes) {
      return toast.custom(<Warning message={"please select affiliation"} />);
    }
    if (!affiliation) {
      return toast.custom(<Warning message={"please select affiliation"} />);
    }

    const admin = JSON.parse(localStorage.getItem("admin"));

    const payload = {
      ...data,
      affiliation:
        typeof affiliation === "string" ? affiliation : affiliation?.value,
      store: typeof store === "string" ? store : store?.value,
      device,
      notes,
      gettingType,
      givingType: userGettingType,
      createdBy: admin?._id,
    };

    if (userGettingType === "amount") {
      payload.newUserOfferAmount = newUserOfferAmount;
      payload.oldUserOfferAmount = oldUserOfferAmount;
      payload.newUserOfferPercent = null;
      payload.oldUserOfferPercent = null;
    }
    if (userGettingType === "percent") {
      payload.newUserOfferPercent = newUserOfferPercent;
      payload.oldUserOfferPercent = oldUserOfferPercent;
      payload.newUserOfferAmount = null;
      payload.oldUserOfferAmount = null;
    }
    dispatch(updateStoreCategory({ categoryId, payload }));
  };
  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStore = (values) => {
    setStore(values?.value);
    dispatch(fetchStoreDetails(values?.value));
  };

  const handleBaseLinkClick = async () => {
    if (store) {
      const selectedStore = allStoresList.find((item) =>
        (item._id === typeof store) === "string" ? store : store?.value
      );
      setValue("sectionLink", selectedStore?.affiliateLink);
    } else {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
    }
  };

  const handleGettingTypeChange = (type) => {
    setGettingType(type);
    // Reset the rate fields
    setValue("gettingNewUserRate", "");
    setValue("gettingOldUserRate", "");
  };
  return (
    <CContainer fluid>
      <h3 className="text-center">Edit Store Category</h3>
      <CRow className="justify-content-center">
        <CCol md={12} lg={9} xl={9}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">App Category</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="checkbox"
                  checked={device == "app"}
                  name=""
                  id=""
                  onChange={(e) => {
                    if (e.target.checked) {
                      setDevice("app");
                    } else {
                      setDevice("website");
                    }
                  }}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Category Store (Stores) </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={store}
                  array={allStoresList}
                  handleSelectedValue={handleSelectStore}
                  placeholder={"Select Store..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Affiliation</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={affiliation}
                  array={allAffiliationsList}
                  handleSelectedValue={handleSelectAffiliations}
                  placeholder={"Select Partners..."}
                />
              </CCol>
            </CRow>
            <TextAreaField
              state={"name"}
              title={"Category Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <TextAreaField
              state={"description"}
              title={"Category Description"}
              type={"text"}
              setState={register}
              error={errors?.description?.message}
            />
            <CRow className="my-5">
              <CCol
                md={3}
                lg={3}
                xl={3}
                className="d-flex justify-content-start align-items-center"
              >
                <p className="me-3 my-auto text-capitalize ">
                  Getting Cashback Type
                </p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className=" ">
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"percent"}
                    checked={gettingType === "percent"}
                    onChange={(e) => handleGettingTypeChange(e.target.value)}
                  />
                  <label htmlFor="">Percent</label>
                </div>
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"amount"}
                    checked={gettingType === "amount"}
                    onChange={(e) => handleGettingTypeChange(e.target.value)}
                  />
                  <label htmlFor="">Amount</label>
                </div>
              </CCol>
            </CRow>
            <InputField
              state={"gettingNewUserRate"}
              title={"New  User Getting"}
              type="decimal"
              step="0.01"
              setState={register}
              error={errors.gettingNewUserRate?.message}
            />
            <InputField
              state={"gettingOldUserRate"}
              title={"Old User - getting"}
              type="decimal"
              step="0.01"
              setState={register}
              error={errors.gettingOldUserRate?.message}
            />
            <CRow className="my-5">
              <CCol
                md={3}
                lg={3}
                xl={3}
                className="d-flex justify-content-start align-items-center"
              >
                <p className="me-3 my-auto text-capitalize ">
                  Giving Cashback Type
                </p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className=" ">
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"percent"}
                    checked={userGettingType === "percent"}
                    onChange={(e) => setUserGettingType(e.target.value)}
                  />
                  <label htmlFor="">Percent</label>
                </div>
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"amount"}
                    checked={userGettingType === "amount"}
                    onChange={(e) => setUserGettingType(e.target.value)}
                  />
                  <label htmlFor="">Amount</label>
                </div>
              </CCol>
            </CRow>
            {userGettingType === "amount" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    Old User Amount
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"Old User Amount"}
                    type="decimal"
                    step={0.01}
                    value={oldUserOfferAmount}
                    onChange={(e) => setOldUserOfferAmount(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}

            {userGettingType === "percent" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    Old User Percentage
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"Old User Percentage"}
                    type="decimal"
                    step={0.01}
                    value={oldUserOfferPercent}
                    onChange={(e) => setOldUserOfferPercent(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}
            {userGettingType === "amount" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    New User Amount
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"New User Amount"}
                    type="decimal"
                    step={0.01}
                    value={newUserOfferAmount}
                    onChange={(e) => setNewUserOfferAmount(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}
            {userGettingType === "percent" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    New User Percentage
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"New User Percentage"}
                    type="decimal"
                    step={0.01}
                    value={newUserOfferPercent}
                    onChange={(e) => setNewUserOfferPercent(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}

            <TextAreaField
              state={"sectionLink"}
              title={"Section Link"}
              type={"text"}
              setState={register}
              error={errors?.sectionLink?.message}
            />
            <CRow>
              <div className="text-end  ">
                <CButton
                  onClick={() => {
                    handleBaseLinkClick();
                  }}
                  className="text-center"
                  style={{
                    marginTop: -30,
                    backgroundColor: "#4BAAC8",
                    borderColor: "#4BAAC8",
                    color: "white",
                  }}
                >
                  Base Link
                </CButton>
              </div>
            </CRow>

            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Notes</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor content={notes} setData={setNotes} />
              </CCol>
            </CRow>
            <CRow>
              <div className="text-center mt-4 pt-4">
                <CButton
                  type="reset"
                  onClick={() =>
                    navigate("/store-categories/all-store-categories")
                  }
                  className="text-light ms-auto mx-1"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  type="submit"
                  className="text-light me-auto mx-1"
                  color="success"
                >
                  Create
                </CButton>
              </div>
            </CRow>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default EditStoreCategory;
