import React, { useEffect, useState } from "react";
import {
  <PERSON>utton,
  <PERSON>ol,
  <PERSON>ontainer,
  CForm,
  CFormCheck,
  CFormSwitch,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import {
  cilAirplay,
  cilArrowBottom,
  cilGlobeAlt,
  cilPen,
  cilRouter,
  cilTrash,
} from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import { useNavigate, useParams } from "react-router-dom";
import { CSmartPagination } from "@coreui/react-pro";
import "./store.css";
import { useDispatch, useSelector } from "react-redux";
import {
  createTrendingStore,
  deleteStore,
  deleteTrendingStore,
  fetchAllAffiliationsList,
  fetchAllStores,
  handleOpenModal,
  makeStoreTrending,
  removeFromTrendingStore,
  updateStatus,
  updateStoreAutoCheckStatus,
  updateStoreDeepLinkStatus,
  updateStoreIsSpecialStatus,
  updateStorePriority,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import ReadMore from "src/components/text/ReadMore";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllStores = () => {
  const [sort, setSort] = useState(0);
  const [storeType, setStoreType] = useState(0);
  const [offers, setOffers] = useState(2);
  const [status, setStatus] = useState(2);
  const [partner, setPartner] = useState("");
  const [campaignType, setCampaign] = useState("");
  const [show, setShow] = useState(true);
  //variables for filtering
  const [refresh, setRefresh] = useState(false);
  // variables for sorting
  const [search, setSearch] = useState("");
  const navigate = useNavigate();
  const { storeId } = useParams();
  const dispatch = useDispatch();
  const {
    loading,
    page,
    pages,
    pageSize,
    allStores,
    instantStores,
    instantActiveStores,
    activeStores,
    inactiveStores,
    allStoresCount,
  } = useSelector((state) => state.store);
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
  }, [dispatch]);

  useEffect(() => {
    if (storeId !== "null") {
      dispatch(fetchAllStores({ store: storeId }));
    } else {
      dispatch(
        fetchAllStores({
          storeType,
          sort,
          offer: offers,
          campaignType,
          affiliation: partner,
        })
      );
    }
  }, [
    storeId,
    dispatch,
    refresh,
    // storeType, sort, offers, partner, campaignType
  ]);

  const handleFilter = async () => {
    dispatch(
      fetchAllStores({
        search: search,
        storeType,
        sort,
        offer: offers,
        campaignType,
        affiliation: partner,
      })
    );
  };

  const handleReset = async () => {
    setSearch("");
    setStoreType(0);
    setSort(0);
    setOffers(2);
    setCampaign("");
    setPartner("");
    dispatch(
      fetchAllStores({
        search: "",
        storeType: 0,
        sort: 0,
        offer: 2,
      })
    );
  };

  const handlePagination = (value) => {
    dispatch(
      fetchAllStores({
        page: value,
        search: search,
        storeType,
        sort,
        offer: offers,
        campaignType,
        affiliation: partner,
      })
    );
  };

  const handleSearch = (value) => {
    setSearch(value);
    // dispatch(fetchAllStores({ search: value, storeType, sort }));
  };

  const handleSelectAffiliations = (values) => {
    setPartner(values?.value);
  };
  const handleSelectCampaignType = (values) => {
    setCampaign(values?.value);
  };

  return (
    <CContainer>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="my-2 ">
        <CCol lg={4} xs={10} sm={6} className="text-center d-flex  me-auto">
          {/* <CForm> */}
          <input
            id="searchStoreXCCC"
            type="text"
            onChange={(e) => {
              e.preventDefault();
              handleSearch(e.target.value);
            }}
            className="w-100 rounded border p-2 "
            placeholder="Search Store..."
            onKeyPress={(e) => {
              if (e.key == "Enter") {
                handleFilter();
              }
            }}
          />
          {/* </CForm> */}
        </CCol>
      </CRow>
      <CRow className={` ${show ? "" : "d-none"} border rounded pt-3`}>
        <CRow className=" my-2 p-4">
          <CCol sm={12}>
            <p className="h6">Status :</p>
          </CCol>
          <CCol sm={3}>
            <input
              type="radio"
              name="exampleRadio"
              id="offers_1"
              value="0"
              className="mx-1"
              onClick={() => setStoreType(1)}
              checked={storeType == 1}
            />
            <label htmlFor="offers_1">Show only active offers</label>
          </CCol>
          <CCol sm={3}>
            <input
              type="radio"
              name="exampleRadio"
              id="offers_1"
              value="0"
              className="mx-1"
              onClick={() => setStoreType(2)}
              checked={storeType == 2}
            />
            <label htmlFor="offers_1">Show only Inactive offers</label>
          </CCol>
          <CCol className="" sm={3}>
            <input
              type="radio"
              name="exampleRadio"
              id="offers_2"
              onClick={() => setStoreType(0)}
              checked={storeType == 0}
              className="mx-1"
              defaultChecked
            />
            <label htmlFor="offers_2">Show all offers</label>
          </CCol>

          <CCol className="my-2" sm={12}>
            <p className="h6">Offer Count:</p>
          </CCol>
          <CCol sm={3}>
            <input
              type="radio"
              name="exampleRadios"
              id="zero_offers_1"
              className="mx-1"
              checked={offers == 0}
              onClick={() => setOffers(0)}
            />
            <label htmlFor="zero_offers_1">Zero(0) Offers</label>
          </CCol>
          <CCol sm={3}>
            <input
              type="radio"
              name="exampleRadios"
              id="zero_offers_2"
              className="mx-1"
              checked={offers == 1}
              onClick={() => setOffers(1)}
            />
            <label htmlFor="zero_offers_2">One(1) Offers</label>
          </CCol>
          <CCol sm={3}>
            <input
              type="radio"
              name="exampleRadios"
              id="zero_offers_3"
              className="mx-1"
              checked={offers == 2}
              onClick={() => setOffers(2)}
              defaultChecked
            />
            <label htmlFor="zero_offers_3">All Offers</label>
          </CCol>
          <CRow className=" my-3 ">
            <CCol className="" xs={12} sm={4}>
              <SearchAndSelect
                array={allAffiliationsList}
                handleSelectedValue={handleSelectAffiliations}
                placeholder={"Select Partners..."}
              />
            </CCol>
            <CCol className="" xs={12} sm={4}>
              <SearchAndSelect
                array={[
                  { name: "CPS", _id: "CPS" },
                  { name: "CPV", _id: "CPV" },
                  { name: "CPA", _id: "CPA" },
                ]}
                handleSelectedValue={handleSelectCampaignType}
                placeholder={"Select Campaign type..."}
              />
            </CCol>
          </CRow>
          <CCol className="text-end mt-2" sm={12}>
            <button
              type="button"
              onClick={
                () => handleReset()
                // setRefresh(!refresh)
              }
              className="border rounded px-4 py-1 text-danger border-danger shadow fw-bold"
            >
              RESET
            </button>
            <button
              type="button"
              onClick={handleFilter}
              className="border rounded px-4 py-1 text-primary border-primary shadow fw-bold"
            >
              FILTER
            </button>
          </CCol>
        </CRow>
      </CRow>

      <CRow className="">
        <SimpleBox
          show={storeType === 0 ? true : false}
          setState={setStoreType}
          title={"All Stores"}
          data={allStoresCount}
          value={0}
        />
        <SimpleBox
          show={storeType === 1 ? true : false}
          setState={setStoreType}
          title={"All Active Stores"}
          data={activeStores}
          value={1}
        />
        <SimpleBox
          show={storeType === 2 ? true : false}
          setState={setStoreType}
          title={"All inactive Stores"}
          data={inactiveStores}
          value={2}
        />
        <SimpleBox
          show={storeType === 3 ? true : false}
          setState={setStoreType}
          title={"All instant stores"}
          data={instantActiveStores}
          value={3}
        />
        <SimpleBox
          show={storeType === 4 ? true : false}
          setState={setStoreType}
          title={"All inactive instant  Stores"}
          data={instantStores}
          value={4}
        />
      </CRow>
      <CRow>
        <CCol className="ms-auto text-end ">
          <CButton
            onClick={() => navigate("/store/create-store")}
            className="btn  btn-sm text-light ms-auto m-2 "
            color="primary "
          >
            Create Store
          </CButton>
        </CCol>
      </CRow>
      <CRow
        style={
          {
            // marginRight: -40,
            // justifyContent: "center",
          }
        }
      >
        <CRow className="my-2">
          <CCol className=" fw-bold py-2 " sm={1}>
            Sort By:
          </CCol>
          <CCol className="d-flex justify-content-around  p-2 " sm={12} md={6}>
            <SortButton
              state={1}
              setState={setSort}
              title={" Highest # of offers"}
              rotate={sort === 1}
            />
            <SortButton
              state={2}
              setState={setSort}
              title={" Lowest # of offers"}
              rotate={sort === 2}
            />
            <SortButton
              state={3}
              setState={setSort}
              title={"  Highest Priority"}
              rotate={sort === 3}
            />
          </CCol>
          <CCol className="d-flex justify-content-between  p-2 " sm={12} md={5}>
            <SortButton
              state={4}
              setState={setSort}
              title={"  Lowest Priority"}
              rotate={sort === 4}
            />

            <SortButton
              state={5}
              setState={setSort}
              title={"Store Name"}
              rotate={sort === 5}
            />
            <SortButton
              state={6}
              setState={setSort}
              title={"Last Updated"}
              rotate={sort === 6}
            />
          </CCol>
        </CRow>
      </CRow>

      {/* <CRow
        style={{
          paddingLeft: 0,
          paddingRight: 0,
          marginRight: -40,
        }}
      > */}
      <div
        style={{
          overflowX: "auto",
          marginRight: -40,
          whiteSpace: "nowrap",
          position: "relative",
          paddingRight: "16px",
        }}
      >
        <CTable
          align="middle"
          className="m-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive="xxl"
          style={{
            width: "100%",
            margin: 0,
            padding: 0,
            minWidth: "1000px",
          }}
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell> NO </CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
              <CTableHeaderCell> Bg color</CTableHeaderCell>
              <CTableHeaderCell> Logo</CTableHeaderCell>
              <CTableHeaderCell>Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliations</CTableHeaderCell>
              <CTableHeaderCell>Store CB</CTableHeaderCell>
              <CTableHeaderCell>Home Offer</CTableHeaderCell>
              <CTableHeaderCell>Link</CTableHeaderCell>
              <CTableHeaderCell>priority</CTableHeaderCell>
              <CTableHeaderCell>Status</CTableHeaderCell>
              <CTableHeaderCell>Deep Link</CTableHeaderCell>
              <CTableHeaderCell>Auto Track </CTableHeaderCell>
              <CTableHeaderCell>Special</CTableHeaderCell>
              <CTableHeaderCell>Valid Offers</CTableHeaderCell>
              <CTableHeaderCell>Active Offers</CTableHeaderCell>
              <CTableHeaderCell>Minimum Amount</CTableHeaderCell>
              <CTableHeaderCell>changed By</CTableHeaderCell>
              <CTableHeaderCell>Reliability</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allStores?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={
                    item?.isDeleted ? "danger" : item?.active ? "" : "warning"
                  }
                  key={item._id}
                >
                  <CTableDataCell>
                    <div>{item?.uid} </div>
                  </CTableDataCell>
                  <CTableDataCell
                    className="text-center p-2"
                    style={{ minWidth: "150px" }}
                  >
                    <div className="d-flex flex-wrap justify-content-center gap-1">
                      <CTooltip content="edit store">
                        <button
                          type="button"
                          onClick={() =>
                            navigate(`/store/edit-store/${item._id}`)
                          }
                          className="border text-primary rounded shadow px-2 py-1"
                        >
                          <CIcon size="sm" icon={cilPen} title={item?.name} />
                        </button>
                      </CTooltip>
                      <CTooltip
                        content={
                          item?.trending
                            ? "remove from Trending"
                            : "make it Trending"
                        }
                      >
                        {item?.trending ? (
                          <button
                            type="button"
                            className="text-danger border rounded shadow px-2 py-1"
                            onClick={async () => {
                              const resData = await dispatch(
                                removeFromTrendingStore(item._id)
                              ).unwrap();
                            }}
                          >
                            <CIcon icon={cilRouter} />
                          </button>
                        ) : (
                          <button
                            type="button"
                            className="text-info border rounded shadow px-2 py-1"
                            onClick={async () => {
                              await dispatch(
                                makeStoreTrending(item._id)
                              ).unwrap();
                            }}
                          >
                            <CIcon icon={cilGlobeAlt} />
                          </button>
                        )}
                      </CTooltip>
                      <CTooltip
                        content={
                          !item?.isDeleted ? "delete store" : "restore store"
                        }
                      >
                        <button
                          type="button"
                          onClick={() => dispatch(deleteStore(item._id))}
                          className={`${
                            !item.isDeleted ? "text-secondary" : "text-danger"
                          } border rounded shadow px-2 py-1`}
                        >
                          <CIcon size="sm" icon={cilTrash} title={item.name} />
                        </button>
                      </CTooltip>
                    </div>
                  </CTableDataCell>

                  <CTableDataCell>
                    <div
                      style={{
                        width: 50,
                        height: 50,
                        backgroundColor: item?.bgColor,
                      }}
                      className="border rounded shadow "
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      alt="logo"
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.logo?.secureUrl })
                        )
                      }
                      width={100}
                      src={item?.logo?.secureUrl}
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.affiliation[0]?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.storeOffer}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.homeOffer?.cashbackTitle}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.affiliateLink} />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className=" text-center">
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="w-100 border rounded p-2 "
                      value={item?.priority}
                      onChange={(e) =>
                        dispatch(
                          updateStorePriority({
                            storeId: item?._id,
                            priority: e.target.value,
                          })
                        )
                      }
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="form-check form-switch ">
                      <input
                        className="form-check-input"
                        onChange={() => dispatch(updateStatus(item?._id))}
                        type="checkbox"
                        role="switch"
                        id="flexSwitchCheckChecked"
                        checked={item?.active}
                        aria-checked="true"
                      />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="form-check form-switch ">
                      <input
                        className="form-check-input"
                        onChange={() =>
                          dispatch(updateStoreDeepLinkStatus(item?._id))
                        }
                        type="checkbox"
                        role="switch"
                        id="flexSwitchCheckChecked"
                        checked={item?.deepLinkEnable}
                        aria-checked="true"
                      />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="form-check form-switch ">
                      <input
                        className="form-check-input"
                        onChange={() =>
                          dispatch(updateStoreAutoCheckStatus(item._id))
                        }
                        type="checkbox"
                        role="switch"
                        id="flexSwitchCheckChecked"
                        checked={item?.autoCheck}
                      />
                      <p>{item?.autoCheck ? "Yes" : "No"} </p>
                    </div>
                  </CTableDataCell>

                  <CTableDataCell>
                    <CFormCheck
                      onClick={() =>
                        dispatch(updateStoreIsSpecialStatus(item._id, 4))
                      }
                      type="radio"
                      name={`flexRadioDefault${index}`}
                      id="flexRadioDefault1"
                      label="None"
                      defaultChecked={item?.isSpecial === "none" ? true : false}
                    />
                    <CFormCheck
                      onClick={() =>
                        dispatch(updateStoreIsSpecialStatus(item._id, 4))
                      }
                      type="radio"
                      name={`flexRadioDefault${index}`}
                      id="flexRadioDefault2"
                      label="Special"
                      defaultChecked={
                        item?.isSpecial === "special" ? true : false
                      }
                    />
                    <CFormCheck
                      onClick={() =>
                        dispatch(updateStoreIsSpecialStatus(item._id, 4))
                      }
                      type="radio"
                      name={`flexRadioDefault${index}`}
                      id="flexRadioDefault3"
                      label="New"
                      defaultChecked={item?.isSpecial === "new" ? true : false}
                    />
                    <CFormCheck
                      onClick={() =>
                        dispatch(updateStoreIsSpecialStatus(item._id, 4))
                      }
                      type="radio"
                      name={`flexRadioDefault${index}`}
                      id="flexRadioDefault4"
                      label="High"
                      defaultChecked={item?.isSpecial === "high" ? true : false}
                    />
                    <CFormCheck
                      onClick={() =>
                        dispatch(updateStoreIsSpecialStatus(item._id, 4))
                      }
                      type="radio"
                      name={`flexRadioDefault${index}`}
                      id="flexRadioDefault5"
                      label="100%"
                      defaultChecked={item?.isSpecial === "100%" ? true : false}
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.offerId?.reduce(
                        (acc, curr) => acc + curr.active,
                        0
                      )}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.minimumAmount}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.updatedBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-center">{item?.reliability}</div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {/* <div
          className="horizontal-scroll-indicator"
          style={{
            position: "absolute",
            bottom: "0",
            right: "0",
            width: "100%",
            height: "4px",
            background: "linear-gradient(to right, #000, #fff)",
          }}
        ></div> */}
      </div>
      {loading && <LoadingComponent />}
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllStores;

const Icon = ({ show }) => {
  return (
    <span
      className={`position-absolute bottom-0 end-0 ${show ? "" : "d-none"}`}
    >
      <img
        src="https://help.healthycities.org/hc/en-us/article_attachments/209780927"
        className=" "
        width={25}
        alt=""
      />
    </span>
  );
};

const Boxes = ({ show, value, data, title, setState }) => {
  return (
    <CCol
      xs={12}
      sm={6}
      md={4}
      lg={3}
      xl={2}
      className="d-flex justify-content-center "
    >
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
      <div
        onClick={() => setState(value)}
        className={`border my-2  rounded position-relative bg-white main-buttons btn pb-1 ${
          show ? "shadow border shadow" : " "
        }  text-center `}
      >
        <Icon show={show} />
        <div className="border border-info h-50 rounded bg-info d-flex ">
          <CIcon
            icon={cilAirplay}
            height={30}
            className=" text-white m-auto mt-3"
          />
        </div>
        <p className="">{title}</p>
        <p className="fw-bold ">{data}</p>
      </div>
    </CCol>
  );
};

const SimpleBox = ({ show, value, data, title }) => {
  return (
    <CCol
      xs={12}
      sm={6}
      md={4}
      lg={3}
      xl={2}
      className="d-flex justify-content-center align-items-center"
    >
      <div
        className={`border my-2 rounded position-relative bg-white text-center`}
        style={{
          padding: "10px", // Add 10px padding around the content
          width: "100%", // Ensure it takes full width of the column
          minHeight: "100px",
          height: "auto", // Remove fixed height
          display: "flex", // Flexbox to center content
          flexDirection: "column", // Stack content vertically
          justifyContent: "center", // Center content vertically
          alignItems: "center", // Center content horizontally
        }}
      >
        <p className="mt-2 mb-0">{title}</p>
        <p
          className="fw-bold text-center mb-0"
          style={{
            fontSize: 16,
          }}
        >
          {data}
        </p>
      </div>
    </CCol>
  );
};
const SortButton = ({ state, setState, title, rotate }) => {
  return (
    <button
      type="button"
      className={`${
        rotate ? "border-bottom border-primary border-1 bg-none" : " "
      }`}
      onClick={() => setState(state)}
    >
      {title}
      <CIcon
        className={`${rotate ? "rotate text-danger" : " reverse"} `}
        icon={cilArrowBottom}
      />
    </button>
  );
};
