/* Your existing CSS */
.form-multi-select-all {
  border: none !important;
  margin-left: rem; /* This line seems incorrect. Use a specific value, e.g., 1rem */
  width: 100%;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.dropdown-menu {
  width: 97%;
}

button {
  border: none;
  outline: none;
}

.form-multi-select-tag-delete {
  border-radius: 5px;
  background: none;
  padding: 5px;
}

.rotate {
  transition: all 0.4s ease;
  transform: rotate(180deg);
}

.reverse {
  transition: all 0.4s ease;
  transform: rotate(0deg);
}
