import { cilPencil, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import {
  deleteSubCategory,
  fetchAllSubCategories,
  handleOpenModal,
} from "src/redux/features";

function AllCategories() {
  const [admin, setAdmin] = useState(null);
  const [search, setSearch] = useState("");
  //previews
  const [category, setCategory] = useState("");
  const [preview, setPreview] = useState(false);
  const { categoryId } = useParams();

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { page, pages, pageSize, allSubCategory, loading } = useSelector(
    (state) => state.subCategory
  );

  useEffect(() => {
    const adminInfo = localStorage.getItem("adminInfo");
    if (adminInfo) {
      const data = JSON.parse(adminInfo);
      setAdmin(data.admin);
    }
    if (categoryId !== "null") {
      dispatch(fetchAllSubCategories({ category: categoryId }));
    } else {
      dispatch(fetchAllSubCategories({}));
    }
  }, [categoryId, dispatch]);

  const handleSearch = async (value) => {
    setSearch(value);
    if (categoryId != "null") {
      dispatch(fetchAllSubCategories({ category: categoryId, search: value }));
    } else {
      dispatch(fetchAllSubCategories({ search: value }));
    }
  };
  const handlePagination = async (value) => {
    if (categoryId != "null") {
      dispatch(
        fetchAllSubCategories({ category: categoryId, search, page: value })
      );
    } else {
      dispatch(fetchAllSubCategories({ search, page: value }));
    }
  };

  const handleDelete = (item) => {
    setCategory(item);
    setPreview(true);
  };

  const handleDeleteSubCategory = async () => {
    dispatch(deleteSubCategory(category?._id));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol md={6} className="">
          <input
            type="text"
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
            className="my-2 p-1 px-2 rounded border"
            placeholder="Search..."
          />
        </CCol>
        <CCol md={6} className="d-flex justify-content-end ">
          <button
            type="button"
            onClick={() => navigate("/category/create-sub-category")}
            className=" my-2 btn btn-primary btn btn-sm"
          >
            Create Sub-Category
          </button>
        </CCol>
      </CRow>
      <CRow className="">
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center"> NO </CTableHeaderCell>
              <CTableHeaderCell>Image</CTableHeaderCell>
              <CTableHeaderCell>Categories</CTableHeaderCell>
              <CTableHeaderCell>created by</CTableHeaderCell>
              <CTableHeaderCell>main Category</CTableHeaderCell>
              <CTableHeaderCell>created at</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allSubCategory?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={item.active ? "" : "danger"}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <div>{item?.uid}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className="shadow border rounded h-auto"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.image?.secureUrl })
                        )
                      }
                      width={50}
                      src={item?.image?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.category?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <CTooltip content="edit category">
                      <button
                        type="button"
                        className="text-success  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/category/update-sub-category/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content="delete category">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDelete(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete this category ?"}
        setState={handleDeleteSubCategory}
        visible={preview}
        setVisible={setPreview}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllCategories;
