import React, { useEffect } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CContainer,
  CForm,
  CRow,
} from "@coreui/react";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAdminDetails,
  fetchAllMiddlewares,
  updateAdminDetails,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import SelectField2 from "src/components/inputs/SelectField2";

const schema = yup
  .object({
    name: yup.string().required(),
    email: yup.string().required(),
    role: yup.string().required(),
  })
  .required();
const UpdateAdmin = () => {
  const { allMiddlewares } = useSelector((state) => state.middleware);
  const { admin, loading } = useSelector((state) => state.admin);
  const dispatch = useDispatch();

  const navigate = useNavigate();
  const { adminId } = useParams();

  useEffect(() => {
    dispatch(fetchAllMiddlewares({}));
    if (adminId) {
      dispatch(fetchAdminDetails(adminId));
    }
  }, [adminId, dispatch]);

  useEffect(() => {
    if (admin) {
      reset(
        { name: admin?.name, email: admin?.email, role: admin?.role },
        { keepErrors: true, keepDirty: true }
      );
    }
  }, [admin]);

  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = async (payload) => {
    const res = await dispatch(
      updateAdminDetails({ adminId, payload })
    ).unwrap();
    if (res?.success) {
      navigate("/admins/all-admins");
    }
  };

  return (
    <CContainer>
      <CRow className="justify-content-center">
        <CCol md={9} lg={9} xl={6}>
          <CCard className="mx-4">
            <CCardBody className="p-4">
              <CForm onSubmit={handleSubmit(onSubmit)}>
                <h2>Edit</h2>
                <p className="text-medium-emphasis">Update admin details</p>
                <InputField
                  state={"name"}
                  title={"user Name"}
                  type={"text"}
                  setState={register}
                  error={errors?.name?.message}
                />
                <InputField
                  state={"email"}
                  title={"email"}
                  type={"text"}
                  setState={register}
                  error={errors?.email?.message}
                />

                <SelectField2
                  state={"role"}
                  title={"role"}
                  setState={register}
                  array={[...allMiddlewares, { name: "super admin" }]}
                  error={errors?.role?.message}
                />

                <div className="d-flex">
                  <CButton
                    type="reset"
                    onClick={() => navigate("/admins/all-admins")}
                    className="text-light ms-auto mx-1"
                    color="danger"
                  >
                    Cancel
                  </CButton>
                  <CButton
                    type="submit"
                    className="text-light me-auto mx-1"
                    color="success"
                  >
                    Update
                  </CButton>
                </div>
              </CForm>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </CContainer>
  );
};

export default UpdateAdmin;
