import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import {
  <PERSON>utton,
  CCol,
  CContainer,
  CForm,
  CFormCheck,
  CFormInput,
  CFormLabel,
  CModal,
  CModalBody,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cibLetsEncrypt,
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
  cilTrash,
} from "@coreui/icons";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import "../admin.css";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAdmins,
  fetchAllMiddlewares,
  permanentDeleteAdmin,
  updateAdminPassword,
  updateAdminRole,
  updateAdminStatus,
} from "src/redux/features";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllAdmins = () => {
  const navigate = useNavigate();
  const [adminDetails, setAdminDetails] = useState("");
  // password reset
  const [preview, setPreview] = useState(false);
  const [admin, setAdmin] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allAdmins, page, pages, loading } = useSelector(
    (state) => state.admin
  );
  const { allMiddlewares } = useSelector((state) => state.middleware);

  useEffect(() => {
    const admin = localStorage.getItem("admin");
    if (admin) {
      setAdminDetails(JSON.parse(admin));
    }
    dispatch(fetchAllAdmins({}));
    dispatch(fetchAllMiddlewares({}));
  }, [dispatch]);

  const handleChangeRole = async (role, adminId) => {
    dispatch(updateAdminRole({ adminId, role }));
  };

  const handleBlock = async (id) => {
    dispatch(updateAdminStatus(id));
  };
  const handleChangePassword = (data) => {
    setAdmin(data);
    setPreview(true);
  };
  const handleDeleteClick = (data) => {
    setAdmin(data);
    setVisible(true);
  };

  const deleteAdmin = async () => {
    dispatch(permanentDeleteAdmin(admin._id));
  };

  const handlePagination = (value) => {};

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CCol className="mb-3 text-end ">
        <CButton
          onClick={() => navigate("/admins/create-admin")}
          color="primary"
          className=""
        >
          Create Admin
        </CButton>
      </CCol>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">ID</CTableHeaderCell>
              <CTableHeaderCell>Name</CTableHeaderCell>
              <CTableHeaderCell className="">Username</CTableHeaderCell>
              <CTableHeaderCell className="">roles</CTableHeaderCell>
              <CTableHeaderCell>Status</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allAdmins?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={item?.block && "danger"}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item.name}{" "}
                      {adminDetails?._id === item._id ? (
                        <span className="fw-bold text-info">&#40;YOU&#41;</span>
                      ) : (
                        ""
                      )}
                    </div>
                    <div className="small ">
                      {dateFormatter(item.createdAt)}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div>{item.email}</div>
                  </CTableDataCell>
                  <CTableDataCell className="position-relative ">
                    <select
                      disabled={adminDetails?._id === item._id ? true : false}
                      className={
                        " border border-primary text-capitalize w-75 form-select rounded px-3 py-1"
                      }
                      onChange={(e) =>
                        handleChangeRole(e.target.value, item._id)
                      }
                      name=""
                      id=""
                    >
                      <option value={item.role}>{item.role}</option>
                      {allMiddlewares?.map((itm) => (
                        <option
                          className={`${itm.name === item?.role && "d-none"}`}
                          key={itm._id}
                          value={itm.name}
                        >
                          {itm.name}
                        </option>
                      ))}
                      <option
                        className={"super admin" === item?.role ? "d-none" : ""}
                        key="super-admin"
                        value="super admin"
                      >
                        super admin
                      </option>
                    </select>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="small text-medium-emphasis">
                      {item.block ? "blocked " : "Active "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <CTooltip content="edit admin details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/admins/edit-admin/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.block ? "unblock " : "block "}>
                      <button
                        type="button"
                        className={`${
                          item.block ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        disabled={adminDetails?._id === item._id ? true : false}
                        onClick={() => handleBlock(item._id)}
                      >
                        {item.block ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                    <CTooltip content="change password">
                      <button
                        type="button"
                        className=" text-info  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleChangePassword(item)}
                      >
                        <CIcon icon={cibLetsEncrypt} />
                      </button>
                    </CTooltip>
                    <CTooltip content="delete admin">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        disabled={adminDetails?._id === item._id ? true : false}
                        onClick={() => handleDeleteClick(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={
          "Deleting admin details is irreversible. Proceed with caution."
        }
        setState={deleteAdmin}
        visible={visible}
        setVisible={setVisible}
      />
      <UpdatePasswordAlert
        admin={admin}
        visible={preview}
        setVisible={setPreview}
      />
    </CContainer>
  );
};

export default AllAdmins;

const UpdatePasswordAlert = ({ admin, visible, setVisible }) => {
  const [showPass, setShowPass] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [conPassword, setConPassword] = useState("");
  const dispatch = useDispatch();

  const handleDelete = async (e) => {
    e.preventDefault();
    const payload = {
      adminId: admin._id,
      email: admin.email,
      password: newPassword,
    };
    dispatch(updateAdminPassword(payload));
  };
  return (
    <CModal visible={visible}>
      <CModalBody className=" ">
        <CForm className="row g-3 px-2 py-4" onSubmit={handleDelete}>
          <CCol xs="auto">
            <CFormLabel htmlFor="staticEmail2" className="">
              Admin Name
            </CFormLabel>
            <CFormInput
              type="text"
              id="staticEmail2"
              className="text-capitalize fw-bold"
              defaultValue={admin?.name}
              readOnly
              plainText
            />
          </CCol>
          <CCol xs="auto">
            <CFormLabel htmlFor="staticEmail2" className="">
              Admin Email
            </CFormLabel>
            <CFormInput
              type="text"
              id="staticEmail2"
              className="fw-bold"
              defaultValue={admin?.email}
              readOnly
              plainText
            />
          </CCol>
          <CCol xs="12">
            <CFormLabel htmlFor="inputPassword2" className="mx-3">
              New Password
            </CFormLabel>
            <CFormInput
              type={showPass ? "text" : "password"}
              id="inputPassword3"
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-75"
              placeholder="New Password"
              required
            />
          </CCol>
          <CCol xs="12">
            <CFormInput
              type={showPass ? "text" : "password"}
              id="inputPassword4"
              onChange={(e) => setConPassword(e.target.value)}
              className="w-75"
              placeholder="Confirm Password"
              required
            />
          </CCol>
          <CCol xs="12">
            <CFormCheck
              onClick={() => setShowPass(!showPass)}
              className="mx-2"
            />
            <CFormLabel htmlFor="inputPassword2" className="mx-2">
              Show Password
            </CFormLabel>
          </CCol>
          <div className="d-flex justify-content-center">
            <button
              type="reset"
              className="btn btn-danger mx-1 my-3 text-white"
              onClick={() => setVisible(false)}
            >
              Cancel
            </button>
            <button
              type="submit"
              className=" btn btn-success  mx-1 my-3 text-white"
            >
              Update
            </button>
          </div>
        </CForm>
      </CModalBody>
    </CModal>
  );
};
