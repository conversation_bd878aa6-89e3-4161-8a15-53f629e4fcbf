import { cilLockLocked, cilLockUnlocked, cilPencil } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CForm,
  CModal,
  CModalBody,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import {
  createPersonalInterest,
  fetchAllPersonalInterests,
  updateTermsAndPrivacyStatus,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllPersonalInterest() {
  const [showModal, setShowModal] = useState(false);
  const [updateData, setUpdateData] = useState(null);
  const dispatch = useDispatch();
  const { page, pages, pageSize, allPersonalInterests, loading } = useSelector(
    (state) => state.personalInterest
  );
  useEffect(() => {
    dispatch(fetchAllPersonalInterests({}));
  }, [dispatch]);

  const handlePagination = (value) => {
    dispatch(fetchAllPersonalInterests({ page: value }));
  };
  const handleDelete = (value) => {
    dispatch(updateTermsAndPrivacyStatus(value));
  };

  const handleCreate = (data = null) => {
    if (data) {
      setUpdateData(data);
      setShowModal(true);
    } else {
      setUpdateData(null);
      setShowModal(true);
    }
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="text-end">
          <button
            type="button"
            onClick={() => handleCreate()}
            className=" my-2 btn btn-primary mx-auto btn-sm"
          >
            Create new personal interest
          </button>
        </div>
      </CRow>
      <CRow>
        <CCol>
          <CTable
            align="middle"
            className="mb-0 border"
            hover
            striped
            bordered
            borderColor="secondary"
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="text-center">No</CTableHeaderCell>
                <CTableHeaderCell>name</CTableHeaderCell>
                <CTableHeaderCell>createdAt</CTableHeaderCell>
                <CTableHeaderCell>Admin</CTableHeaderCell>
                <CTableHeaderCell>Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allPersonalInterests?.map((item) => (
                  <CTableRow
                    v-for="item in tableItems"
                    className=" "
                    color={!item.active ? "danger" : ""}
                    key={item?.uid}
                  >
                    <CTableDataCell className="text-center">
                      <p>{item?.uid}</p>
                    </CTableDataCell>

                    <CTableDataCell className="position-relative ">
                      <div>
                        <p>{item?.name}</p>
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="small ">
                        {dateFormatter(item.createdAt)}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.createdBy?.name} </div>
                    </CTableDataCell>
                    <CTableDataCell className="">
                      <CTooltip content="edit terms and conditions details">
                        <button
                          type="button"
                          className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                          onClick={(e) => handleCreate(item?.name)}
                        >
                          <CIcon icon={cilPencil} />
                        </button>
                      </CTooltip>
                      <CTooltip content={item.active ? "Active " : "InActive "}>
                        <button
                          type="button"
                          className={`${
                            item.active ? "text-success " : " text-danger"
                          } border rounded shadow px-2 py-1 mx-1`}
                          onClick={() => handleDelete(item._id)}
                        >
                          {!item.active ? (
                            <CIcon icon={cilLockLocked} />
                          ) : (
                            <CIcon icon={cilLockUnlocked} />
                          )}
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
        <UpdateDataModal
          data={updateData}
          visible={showModal}
          setVisible={setShowModal}
          dispatch={dispatch}
        />
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllPersonalInterest;

const schema = yup
  .object({
    name: yup.string().required("Personal interest title is required"),
  })
  .required();

const UpdateDataModal = ({ visible, setVisible, dispatch, data = null }) => {
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (data) {
      reset({ name: data }, { keepDirty: true, keepErrors: true });
    }
  }, [data, reset]);

  const onSubmit = (payload) => {
    if (data) {
      dispatch(updatePersonalInterest({ id, payload }));
    } else {
      dispatch(createPersonalInterest(payload));
    }
  };
  return (
    <CModal visible={visible} backdrop="static">
      <CModalBody className="border m-3 rounded">
        <h6 className="text-center mt-2 mb-4">
          {data ? "Update" : "Create"} Personal Interest Title
        </h6>
        <CForm onSubmit={handleSubmit(onSubmit)}>
          <CRow className="mb-2">
            <InputField
              state={"name"}
              title={"Personal Interest Title"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
          </CRow>
          <div className="d-flex justify-content-center">
            <button
              type="reset"
              className=" mx-1 mt-3 text-white border rounded bg-danger px-4 py-1"
              onClick={() => {
                setVisible(false);
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              className=" mx-1 mt-3 text-white border rounded bg-success px-4 py-1"
            >
              Update
            </button>
          </div>
        </CForm>
      </CModalBody>
    </CModal>
  );
};
