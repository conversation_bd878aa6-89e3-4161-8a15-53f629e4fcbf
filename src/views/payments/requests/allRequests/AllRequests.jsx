import {
  cilArrowBottom,
  cil<PERSON><PERSON><PERSON>ine,
  cilLockLocked,
  cilLockUnlocked,
  cilPenAlt,
  cilPhone,
  cilRecycle,
  cilThumbDown,
  cilThumbUp,
  cilTrash,
} from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CContainer,
  CDropdown,
  CDropdownDivider,
  CDropdownItem,
  CDropdownMenu,
  CDropdownToggle,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAffiliationsList,
  fetchAllStoresList,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import {
  approvePaymentRequest,
  fetchAllPaymentRequests,
  rejectPaymentRequest,
} from "src/redux/features/paymentRequest";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllRequests() {
  const [search, setSearch] = useState("");
  const [orderId, setOrderId] = useState("");
  const [remarks, setRemarks] = useState("");
  const [advId, setAdvId] = useState("");
  const [userId, setUserId] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [amount, setAmount] = useState(-1);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allStoresList } = useSelector((state) => state.store);
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { page, pages, pageSize, allPaymentRequests, loading } = useSelector(
    (state) => state.payments
  );

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllPaymentRequests({}));
  }, [dispatch]);

  const handleSearch = async (value) => {
    dispatch(fetchAllPaymentRequests({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllPaymentRequests({ page: value }));
  };

  const handleSelectStatus = (values) => {
    console.log(values?.value, "values");
    dispatch(fetchAllPaymentRequests({ status: values?.value }));
  };
  const handleSelectAffiliation = (values) => {
    dispatch(dispatch(fetchAllPaymentRequests({ affiliation: values?.value })));
  };
  const handleSelectStore = (values) => {
    dispatch(fetchAllPaymentRequests({ store: values?.value }));
  };

  useEffect(() => {
    dispatch(fetchAllPaymentRequests({ startDate, endDate, amount }));
  }, [startDate, endDate, amount]);

  const handleApproveStatus = (id) => {
    dispatch(approvePaymentRequest(id));
  };

  const handleRejectStatus = (id) => {
    dispatch(rejectPaymentRequest(id));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
        alignEnd
      />
      <CRow>
        <CCol md={12}>
          <CCard className="mb-2 border border-warning">
            <CCardHeader className="">
              <CRow>
                <CCol sm={4}>
                  <label htmlFor="" className="w-100">
                    Store Name/User Name/ Earning Id/ Email:
                  </label>
                  <input
                    type="text"
                    className="my-2 w-75 border rounded py-1 px-2 "
                    placeholder="Search...."
                    value={search}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </CCol>
                <CCol sm={4}>
                  <label htmlFor="" className="w-100">
                    Order unique Id:
                  </label>
                  <input
                    type="text"
                    className="my-2 w-75 border rounded py-1 px-2 "
                    placeholder="Search...."
                    value={orderId}
                    onChange={(e) => handleSearchOrderId(e.target.value)}
                  />
                </CCol>
                <CCol sm={4} className="">
                  <label htmlFor="" className="w-100">
                    User Id:{" "}
                  </label>
                  <input
                    type="text"
                    className="my-2 w-75 border rounded py-1 px-2 "
                    placeholder="Search...."
                    value={userId}
                    onChange={(e) => handleSearchUserId(e.target.value)}
                  />
                </CCol>
                <CCol sm={4}>
                  <label htmlFor="" className="w-100">
                    Adv Id:
                  </label>
                  <input
                    type="text"
                    className="my-2 w-75 border rounded py-1 px-2 "
                    placeholder="Search...."
                    value={advId}
                    onChange={(e) => handleSearchAdvId(e.target.value)}
                  />
                </CCol>
                <CCol sm={4}>
                  <label htmlFor="" className="w-100">
                    Remarks:
                  </label>
                  <input
                    type="text"
                    className="my-2 w-75 border rounded py-1 px-2 "
                    placeholder="Search...."
                    value={remarks}
                    onChange={(e) => handleSearchRemarks(e.target.value)}
                  />
                </CCol>
              </CRow>
            </CCardHeader>
            <CCardBody>
              <CRow className="mt-3">
                <CCol xs={12} className="mb-2">
                  <p className="h6"> Date Range :</p>
                  <div className="d-flex">
                    <div>
                      <label htmlFor="">Start Date</label>
                      <input
                        type="date"
                        name=""
                        className="border rounded  p-2 m-2"
                        onChange={(e) => setStartDate(e.target.value)}
                        placeholder=""
                        id=""
                      />
                    </div>
                    <div>
                      <label htmlFor="">End Date</label>
                      <input
                        type="date"
                        name=""
                        className="border rounded  p-2 m-2"
                        onChange={(e) => setEndDate(e.target.value)}
                        id=""
                      />
                    </div>
                  </div>
                </CCol>
                <CCol xs={12} sm={6} md={4} xl={4}>
                  <SearchAndSelect
                    array={[
                      { name: "All", _id: "" },
                      { name: "Pending", _id: "pending" },
                      { name: "Approved", _id: "approved" },
                      { name: "Rejected", _id: "rejected" },
                    ]}
                    handleSelectedValue={handleSelectStatus}
                    placeholder={"Select Status..."}
                  />
                </CCol>
                <CCol xs={12} sm={6} md={4} xl={4}>
                  <SearchAndSelect
                    array={allAffiliationsList}
                    handleSelectedValue={handleSelectAffiliation}
                    placeholder={"Select Affiliation..."}
                  />
                </CCol>
                <CCol xs={12} sm={6} md={4} xl={4}>
                  <SearchAndSelect
                    array={allStoresList}
                    handleSelectedValue={handleSelectStore}
                    placeholder={"Select Store..."}
                  />
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  ms-auto   " sm={8}>
          <button
            type="button"
            className={`${
              amount === 0
                ? "border-bottom border-primary border-3 bg-none"
                : " "
            } border-0  mx-2  `}
            onClick={() => setAmount(amount == 1 ? 0 : 1)}
          >
            Amount
            <CIcon
              className={`${
                amount === 0 ? " rotate text-danger" : "reverse "
              } `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="">NO</CTableHeaderCell>
                <CTableHeaderCell>User Id</CTableHeaderCell>
                <CTableHeaderCell>User</CTableHeaderCell>
                <CTableHeaderCell>Name of User</CTableHeaderCell>
                <CTableHeaderCell>Address</CTableHeaderCell>
                <CTableHeaderCell>Amount </CTableHeaderCell>
                <CTableHeaderCell>Mobile Number</CTableHeaderCell>
                <CTableHeaderCell>UPI</CTableHeaderCell>
                <CTableHeaderCell>Bank Name</CTableHeaderCell>
                <CTableHeaderCell>Branch Name</CTableHeaderCell>
                <CTableHeaderCell>Account Number</CTableHeaderCell>
                <CTableHeaderCell>Holder Name</CTableHeaderCell>
                <CTableHeaderCell>IFSC</CTableHeaderCell>
                <CTableHeaderCell>Status</CTableHeaderCell>
                <CTableHeaderCell>Date Of Request</CTableHeaderCell>
                <CTableHeaderCell>Progress Change Date</CTableHeaderCell>
                <CTableHeaderCell> Notes</CTableHeaderCell>
                <CTableHeaderCell>actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allPaymentRequests?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" key={item._id}>
                    <CTableDataCell className="text-center">
                      <div className="">{item?.uid} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.uid}</div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.name} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.email} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.address}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawAmount}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.phone}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.upi}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.bankName}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.branchName}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.accountNumber}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.holderName}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.bankDetails?.ifsc}</div>
                    </CTableDataCell>

                    <CTableDataCell>
                      <div
                        className={` text-capitalize border shadow rounded p-1 ${
                          item.status === "approved" &&
                          "text-success  border-success"
                        } ${
                          item.status === "pending" &&
                          "text-warning border-warning"
                        } ${
                          item.status === "rejected" &&
                          "text-danger border-danger"
                        }`}
                      >
                        {item?.status}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{dateFormatter(item?.createdAt)}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className={item?.updatedAt ? " " : " d-none"}>
                        {dateFormatter(item?.updatedAt)}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.withdrawer?.userNotes}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <CTooltip content={"approve request"}>
                        <button
                          type="button"
                          className={`text-success border rounded shadow px-2 py-1 mx-1`}
                          onClick={() => handleApproveStatus(item._id)}
                        >
                          <CIcon icon={cilThumbUp} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"reject request"}>
                        <button
                          type="button"
                          className={`text-danger border rounded shadow px-2 py-1 mx-1`}
                          onClick={() => handleRejectStatus(item._id)}
                        >
                          <CIcon icon={cilThumbDown} />
                        </button>
                      </CTooltip>
                      <CDropdown className="d-none" variant="btn-group">
                        <CButton color={"success"}>{"Actions"}</CButton>
                        <CDropdownToggle color={"success"} split />
                        <CDropdownMenu>
                          <CDropdownItem onClick={() => addConfirm(item._id)}>
                            <CIcon icon={cilThumbUp} /> Confirmed
                          </CDropdownItem>
                          <CDropdownDivider />
                          <CDropdownItem
                            onClick={() =>
                              navigate(`/users/earnings/edit/${item?._id}`)
                            }
                          >
                            <CIcon icon={cilPenAlt} /> Edit
                          </CDropdownItem>
                          <CDropdownDivider />
                          <CDropdownItem onClick={() => addCancel(item._id)}>
                            <CIcon icon={cilRecycle} />
                            Process
                          </CDropdownItem>
                          <CDropdownDivider />
                          <CDropdownItem>
                            <CIcon icon={cilPhone} />
                            Contact User
                          </CDropdownItem>
                          <CDropdownDivider />
                          <CDropdownItem onClick={() => addPending(item._id)}>
                            <CIcon icon={cilChartLine} /> User Statistics
                          </CDropdownItem>
                          <CDropdownDivider />
                          <CDropdownItem>
                            <CIcon icon={cilTrash} /> Delete
                          </CDropdownItem>
                        </CDropdownMenu>
                      </CDropdown>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllRequests;
