import {
  cilPenNib,
  cilThumbUp,
  cilXCircle,
  cilTrash,
  cilCloudDownload,
} from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CButton,
  CCol,
  CContainer,
  CModal,
  CModalBody,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteMissingPayment,
  fetchAllAffiliationsList,
  fetchAllMissingCashback,
  fetchAllStoresList,
  handleOpenModal,
  updateMissingCashbackNotes,
  updateMissingCashbackStatus,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import { toast } from "react-hot-toast";
function AllMissingCashback() {
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("");
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [cbDetails, setCBDetails] = useState("");
  const [showNoteModel, setShowNoteModal] = useState(false);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allStoresList } = useSelector((state) => state.store);
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { page, pages, pageSize, allMissingCashback, loading } = useSelector(
    (state) => state.missingCashback
  );

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllMissingCashback({}));
  }, [dispatch]);

  const handleSearch = async (value) => {
    setSearch(value);
    // dispatch(fetchAllMissingCashback({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(
      fetchAllMissingCashback({
        page: value,
        status: status?._id,
        startDate: startDate,
        endDate: endDate,
        store: store?._id,
        affiliation: affiliation?._id,
        search: search,
      })
    );
  };

  const handleSelectStatus = (values) => {
    // console.log(values);
    // dispatch(fetchAllMissingCashback({ status: values?.value }));
    // setStatus(values?.value);
    setStatus({
      _id: values?.value,
      label: values?.label,
    });
  };
  const handleSelectAffiliation = (values) => {
    // setAffiliation(values?.value);
    setAffiliation({
      _id: values?.value,
      label: values?.label,
    });
    // dispatch(fetchAllMissingCashback({ affiliation: values?.value }));
  };
  const handleSelectStore = (values) => {
    // dispatch(fetchAllMissingCashback({ store: values?.value }));
    // setStore(values?.value);

    setStore({
      _id: values?.value,
      label: values?.label,
    });
  };
  const handleUpdateNotes = (data) => {
    setCBDetails(data);
    setShowNoteModal(true);
  };

  const handleUpdateStatus = (id, status) => {
    dispatch(updateMissingCashbackStatus({ cashbackId: id, status: status }));
  };

  const handleDeleteCashback = (id) => {
    dispatch(deleteMissingPayment(id));
  };

  const handleFilter = () => {
    dispatch(
      fetchAllMissingCashback({
        page: 1,
        status: status?._id,
        startDate: startDate,
        endDate: endDate,
        store: store?._id,
        affiliation: affiliation?._id,
        search: search,
      })
    );
  };

  const handleReset = () => {
    setStartDate("");
    setEndDate("");
    setStatus("all");
    setStore("");
    setAffiliation("");
    setSearch("");

    dispatch(
      fetchAllMissingCashback({
        page: 1,
      })
    );
    // updateURLParams("");
  };

  const handleFileClick = (url) => {
    const fileExtension = url.split(".").pop().split("?")[0].toLowerCase();
    if (["pdf", "docx"].includes(fileExtension)) {
      window.open(url, "_blank");
    } else {
      // Dispatch to open modal with image
      dispatch(handleOpenModal({ image: url }));
    }
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol md={4} lg={3} sm={10}>
          <input
            type="text"
            className="w-100 border rounded py-1 px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
        <CRow className="mt-3">
          <CCol xs={12} className="mb-2">
            <p className="h6"> Date Range :</p>
            <div className="d-flex">
              <div>
                <label htmlFor="">Start Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  onChange={(e) => setStartDate(e.target.value)}
                  value={startDate}
                  placeholder=""
                  id=""
                />
              </div>
              <div>
                <label htmlFor="">End Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  id=""
                />
              </div>
            </div>
          </CCol>
          <CCol xs={12} sm={6} md={4} xl={4}>
            <SearchAndSelect
              array={[
                { name: "All", _id: "" },
                { name: "Solved", _id: "solved" },
                { name: "Not-solved", _id: "not-solved" },
                { name: "Rejected", _id: "rejected" },
                { name: "Forwarded", _id: "forwarded" },
              ]}
              handleSelectedValue={handleSelectStatus}
              placeholder={"Select Status..."}
              defaultValue={status}
            />
          </CCol>
          <CCol xs={12} sm={6} md={4} xl={4}>
            <SearchAndSelect
              array={allAffiliationsList}
              handleSelectedValue={handleSelectAffiliation}
              placeholder={"Select Affiliation..."}
              defaultValue={affiliation}
            />
          </CCol>
          <CCol xs={12} sm={6} md={4} xl={4}>
            <SearchAndSelect
              array={allStoresList}
              handleSelectedValue={handleSelectStore}
              placeholder={"Select Store..."}
              defaultValue={store}
            />
          </CCol>
        </CRow>
        <CRow>
          <CCol className="mt-3 d-flex" sm={12}>
            <button
              onClick={
                () => handleReset()
                // setRefresh(!refresh)
              }
              type="reset"
              className="btn-danger btn text-light px-5 py-2 me-auto mx-2 btn-sm"
            >
              reset
            </button>
            <button
              onClick={() => handleFilter()}
              type="submit"
              className="btn-primary btn px-5 py-2 mx-2 ms-auto btn-sm "
            >
              filter
            </button>
          </CCol>
        </CRow>
      </CRow>
      <CRow className="pt-2">
        <CTable align="middle" className=" border " striped hover bordered>
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>actions</CTableHeaderCell>
              <CTableHeaderCell className="">NO</CTableHeaderCell>
              <CTableHeaderCell>User Id</CTableHeaderCell>
              <CTableHeaderCell>Name</CTableHeaderCell>
              <CTableHeaderCell>Email </CTableHeaderCell>
              <CTableHeaderCell>Message</CTableHeaderCell>
              <CTableHeaderCell>Store Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliation</CTableHeaderCell>
              <CTableHeaderCell>User Type</CTableHeaderCell>
              <CTableHeaderCell>Order Date</CTableHeaderCell>
              <CTableHeaderCell>Order Id</CTableHeaderCell>
              <CTableHeaderCell>Item Name</CTableHeaderCell>
              <CTableHeaderCell>Price</CTableHeaderCell>
              <CTableHeaderCell>Coupon Code</CTableHeaderCell>
              <CTableHeaderCell>Device/Platform</CTableHeaderCell>
              <CTableHeaderCell>Status-User</CTableHeaderCell>
              <CTableHeaderCell>Forwarded</CTableHeaderCell>
              <CTableHeaderCell>Notes</CTableHeaderCell>
              <CTableHeaderCell>Invoice</CTableHeaderCell>
              <CTableHeaderCell>Status</CTableHeaderCell>
              <CTableHeaderCell>Reported Date</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allMissingCashback?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  key={item._id}
                  color={
                    item?.status == "solved"
                      ? "success"
                      : item.status == "forwarded"
                      ? "warning"
                      : item?.status == "rejected"
                      ? "danger"
                      : ""
                  }
                >
                  <CTableDataCell>
                    <CTooltip content={"Solve"}>
                      <button
                        type="button"
                        onClick={() => {
                          if (item?.status != "solved") {
                            handleUpdateStatus(item._id, "solved");
                          }
                        }}
                        className={` text-success border-success border rounded shadow px-2 py-1 m-1  `}
                      >
                        {item?.status == "solved" ? "Solved" : "Solve"}
                      </button>
                    </CTooltip>

                    <CTooltip content={"Find click"}>
                      <button
                        type="button"
                        onClick={() => {
                          if (item.click.uid) {
                            window.open(
                              `/clicks/all-clicks?searchParam=${item?.click.uid}`,
                              "_blank" // This opens the URL in a new tab
                            );
                          } else {
                            toast.error("Can't find click with this id");
                          }
                        }}
                        className={` text-success border-success border rounded shadow px-2 py-1 m-1  `}
                      >
                        Find click{" "}
                      </button>
                    </CTooltip>

                    <CTooltip content={"Forward for user"}>
                      <button
                        type="button"
                        onClick={() => {
                          if (!item?.status != "forwarded") {
                            handleUpdateStatus(item._id, "forwarded");
                          }
                        }}
                        className={` text-success border-success border rounded shadow px-2 py-1 m-1  `}
                      >
                        {item?.status == "forwarded"
                          ? "forwarded"
                          : "Forward for user"}
                      </button>
                    </CTooltip>
                    <CTooltip content={"Note"}>
                      <button
                        type="button"
                        onClick={() => handleUpdateNotes(item)}
                        className={` text-info border-info border rounded shadow px-2 py-1 m-1  `}
                      >
                        <CIcon size="sm" className="w-100 " icon={cilPenNib} />
                      </button>
                    </CTooltip>
                    <CTooltip content={"Reject "}>
                      <button
                        type="button"
                        onClick={() => {
                          if (item.status !== "rejected") {
                            handleUpdateStatus(item._id, "rejected");
                          }
                        }}
                        className={` text-danger border-danger border rounded shadow px-2 py-1 m-1  `}
                      >
                        {item?.status == "rejected" ? "Rejected" : "Reject"}
                      </button>
                    </CTooltip>

                    <CTooltip content={"Delete"}>
                      <button
                        type="button"
                        onClick={() => handleDeleteCashback(item._id)}
                        className={` text-danger border-danger border rounded shadow px-2 py-1 m-1  `}
                      >
                        <CIcon size="sm" className="w-100 " icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div className="">{item?.uid} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.user?.uid}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.user?.name}</div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.user?.email} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.message}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {
                        allAffiliationsList.filter(
                          (i) => i._id === item?.affiliation
                        )[0]?.name
                      }
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.userType} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.click?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.orderId}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.title}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.paidAmount}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.couponCode}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.platform}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.isSolved ? "Solved" : "Not Solved"}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.forwarded ? "Yes" : "No"}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.notes}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {item?.invoice?.location &&
                    /\.(pdf|docx)($|\?)/i.test(item.invoice?.location) ? (
                      <button
                        className="btn btn-info btn-sm text-white"
                        onClick={() => handleFileClick(item.invoice?.location)}
                      >
                        <CIcon icon={cilCloudDownload} />
                      </button>
                    ) : (
                      <img
                        className="shadow"
                        style={{ cursor: "pointer" }}
                        onClick={() => handleFileClick(item.invoice?.location)}
                        width={50}
                        src={item.invoice?.location}
                        alt=""
                      />
                    )}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">{item?.status}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
        <PaginationComponent
          page={page}
          pages={pages}
          handlePagination={handlePagination}
        />
        <MissingCashbackNotes
          dispatch={dispatch}
          cbDetails={cbDetails}
          showNoteModel={showNoteModel}
          setShowNoteModal={setShowNoteModal}
        />
      </CRow>
    </CContainer>
  );
}

export default AllMissingCashback;

const MissingCashbackNotes = ({
  dispatch,
  cbDetails,
  showNoteModel,
  setShowNoteModal,
}) => {
  const [notes, setNotes] = useState("");
  const handleUpdateNotes = () => {
    dispatch(updateMissingCashbackNotes({ cashbackId: cbDetails._id, notes }));
    setShowNoteModal(false);
    setNotes("");
  };
  return (
    <CModal visible={showNoteModel} onClose={() => setShowNoteModal(false)}>
      <CModalBody>
        <div className="d-flex justify-content-around my-3">
          <p>
            User Name:{" "}
            <span className="fw-bold text-capitalize">
              {cbDetails?.user?.name}
            </span>
          </p>
          <p>
            User Unique Id: <span className="fw-bold">{cbDetails?.uid}</span>
          </p>
        </div>
        <div className=" ">
          <label htmlFor="">User Notes:</label>{" "}
          <textarea
            name=""
            id=""
            placeholder="write notes..."
            className="w-100 border rounded  p-2"
            rows="5"
            required
            onChange={(e) => setNotes(e.target.value)}
            value={notes}
          ></textarea>
        </div>
      </CModalBody>
      <div className="d-flex justify-content-between align-items-center">
        <CButton
          color="danger"
          size="sm"
          className="w-25 mx-auto my-3 text-white"
          onClick={() => setShowNoteModal(false)}
        >
          Close
        </CButton>
        <CButton
          color="success"
          size="sm"
          className="w-25 mx-auto my-3 text-white"
          onClick={() => handleUpdateNotes()}
        >
          Add Note
        </CButton>
      </div>
    </CModal>
  );
};
