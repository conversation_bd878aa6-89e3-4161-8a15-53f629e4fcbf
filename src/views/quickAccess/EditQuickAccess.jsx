import { <PERSON><PERSON>, <PERSON>ontaine<PERSON>,  CRow } from "@coreui/react";
import React, {  useEffect, useState } from "react";
import CIcon from "@coreui/icons-react";
import { cilCloudUpload, cilCut, cilTrash } from "@coreui/icons";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { useNavigate, useParams } from "react-router-dom";

import { useDispatch, useSelector } from "react-redux";
import {
   updateQuickAccess, fetchQuickAccessDetails,
} from "src/redux/features";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";

function EditQuickAccess() {
  const [image, setImage] = useState(null);
  const [title, setTitle] = useState("");
  const [redirectUrl, setRedirectUrl] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {quickAccessId} = useParams()
  const {quickAccessDetails} = useSelector((state)=>state.quickAccess)
  
  useEffect(() => {
    if(quickAccessId){
        dispatch(fetchQuickAccessDetails(quickAccessId))
    }
  }, [quickAccessId])

  useEffect(() => {
    if(quickAccessDetails){
        setTitle(quickAccessDetails?.title)
        setRedirectUrl(quickAccessDetails?.redirectUrl)
        setImage({
            publicId:quickAccessDetails?.icon?.publicId,
            secureUrl:quickAccessDetails?.icon?.secureUrl
        })
    }
  }, [quickAccessDetails])
  
  

    const handleSubmit = (e) => {
    e.preventDefault();
    
    if(!image){
      return toast.custom(<Danger message={"please update image"}/>)
    }
    const payload = {
title,
redirectUrl,
icon:{
  publicId:image.publicId,
  secureUrl:image.secureUrl
}
    }
    
    dispatch(updateQuickAccess({quickAccessId:quickAccessDetails._id,payload}));
  };

  return (
      <CContainer fluid>
        <CRow className="">
            <div className="mx-5 mb-2 ">
              <h4 className="text-secondary text-center ">Create Quick Access</h4>
            </div>
          <CCol className="border rounded mx-auto " sm={10} md={7}>
            <div
            style={{height:"20rem"}}
              className={` ${
                image ? " " : "offer-frame "
              } my-2 w-100 p-0  hover d-flex justify-content-center align-items-center`}
            >
              <div className="w-100 mx-auto text-center ">
                {image && (
                  <img
                 style={{height:"20rem"}}
                    className="w-auto offer-img my-2"
                    src={image?.secureUrl}
                    alt=""
                  />
                )}
              </div>
            </div>
            <div className="d-flex justify-content-center align-items-center my-2">
              <ImageUploader
                setFile={setImage}
                icon={cilCloudUpload}
                keyword={"Upload Image"}
                accept={".svg, .png"}
              />
              <button
                className="btn btn-outline-danger mx-2 "
                onClick={() => setImage(null)}
              >
                <CIcon icon={cilTrash} />
                Remove
              </button>
            </div>
            <div className="my-3">
                <label className="w-100 my-2" htmlFor="">
                Title
                </label>
                <input
                  className="w-50 p-2 rounded border "
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </div>
              <div className="my-3 ">
                <label className="w-100 my-2" htmlFor="">
                  Redirect Url
                </label>
                <textarea
                  name=""
                  id=""
                  cols="30"
                  rows="5"
                  required
                  value={redirectUrl}
                  placeholder="Redirect url"
                  className="w-75 border rounded"
                  onChange={(e) => setRedirectUrl(e.target.value)}
                ></textarea>
              </div>
              <div className="d-flex justify-content-center align-items-center my-4">
                <button className="btn btn-outline-danger mx-2" type="reset">
                  Cancel
                </button>
                <button onClick={handleSubmit} className="btn btn-outline-success mx-2" type="submit">
                  Update
                </button>
              </div>
          </CCol>
        </CRow>
      </CContainer>
  );
}


export default EditQuickAccess