import { <PERSON><PERSON>, <PERSON>ontainer, CForm, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import CIcon from "@coreui/icons-react";
import { cilCloudUpload, cilTrash } from "@coreui/icons";
import ImageUploader from "src/components/fileManagers/imageUploader";

import { useDispatch, useSelector } from "react-redux";
import {
  createStories, fetchAllStoresList,
} from "src/redux/features";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const currentDateTime = new Date().toISOString().slice(0, 16);
const dummyImage = 'https://i.pinimg.com/474x/07/5e/09/075e09953e21d9ed8d4a143ca2fe991d.jpg'

function CreateStories() {
  const [image, setImage] = useState({ secureUrl: dummyImage, publicId: "" });
  const [expiryDate, setExpiryDate] = useState("");
  const [redirectUrl, setRedirectUrl] = useState("");
  const [description, setDescription] = useState("")
  const [title, setTitle] = useState("")
  const [duration, setDuration] = useState(1000)
  const [buttonText, setButtonText] = useState("")
  const [store, setStore] = useState("")
  const dispatch = useDispatch();
  const { allStoresList } = useSelector((state) => state.store);

  useEffect(() => {
    dispatch(fetchAllStoresList());
  }, [dispatch]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!store) {
      return toast.custom(<Danger message={"please select a store"} />)
    }
    if (!image) {
      return toast.custom(<Danger message={"please update image"} />)
    }
    const payload = {
      store,
      expiryDate,
      redirectUrl,
      title,
      description,
      duration,
      buttonText,
      image
    }
    dispatch(createStories(payload));
  };

  const handleSelectStore = (values) => {
    setStore(values?.value)
  }

  return (
    <CContainer fluid>
      <div className=" ">
        <h4 className="text-secondary text-center ">Create New Mobile Stories</h4>
      </div>
      <CForm onSubmit={handleSubmit}>
        <CRow className="p-2 pt-4 rounded shadow ">
          <CCol className=" " md={6}  >
            <SearchAndSelect
              array={allStoresList}
              handleSelectedValue={handleSelectStore}
              placeholder={"Select Store..."} />
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                title
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Description
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="text"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
              />
            </div>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Button Text
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="text"
                value={buttonText}
                onChange={(e) => setButtonText(e.target.value)}
                required
              />
            </div>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Duration
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="number"
                value={duration}
                min="1000" max="5000"
                onChange={(e) => setDuration(e.target.value)}
                required
              />
            </div>
            <div className="my-3">
              <label className="w-100 my-2" htmlFor="">
                Expiry Date
              </label>
              <input
                className="w-100 p-2 rounded border "
                type="datetime-local"
                id="datetime"
                name="datetime"
                onChange={(e) => setExpiryDate(e.target.value)}
                min={currentDateTime}
                required
              />
            </div>

            <div className="my-3 ">
              <label className="w-100 my-2" htmlFor="">
                Redirect Url
              </label>
              <textarea
                name=""
                id=""
                rows="5"
                required
                value={redirectUrl}
                placeholder="Redirect url"
                className="w-100 border rounded"
                onChange={(e) => setRedirectUrl(e.target.value)}
              />
            </div>

          </CCol>
          <CCol md={6}>
            <div className="w-100 mx-auto text-center ">
              {image && (
                <img
                  style={{ width: "15rem", height: "auto" }}
                  className=" offer-img my-2"
                  src={image?.secureUrl}
                  alt=""
                />
              )}
            </div>
            <div className="d-flex justify-content-center align-items-center my-2">
              <ImageUploader
                setFile={setImage}
                icon={cilCloudUpload}
                keyword={"Upload Image"}
              />
              <button
                type="button"
                className="btn btn-outline-danger mx-2 "
                onClick={() => setImage(null)}
              >
                <CIcon icon={cilTrash} />
                Remove
              </button>
            </div>
          </CCol>
          <div className="d-flex justify-content-center align-items-center my-4">
            <button className="btn btn-outline-danger mx-2" type="reset">
              Cancel
            </button>
            <button className="btn btn-outline-success mx-2" type="submit">
              Create
            </button>
          </div>
        </CRow>
      </CForm>

    </CContainer>
  );
}

export default CreateStories