import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import {
  CButton,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
  cilTrash,
} from "@coreui/icons";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteStory,
  fetchAllStories,
  handleOpenModal,
  updateStoriesActiveStatus,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import { CSmartPagination } from "@coreui/react-pro";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllStories = () => {
  const navigate = useNavigate();
  // password reset
  const [banner, setBanner] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allStories, page, pages, pageSize, loading } = useSelector(
    (state) => state.stories
  );

  useEffect(() => {
    dispatch(fetchAllStories({}));
  }, [dispatch]);

  const handleBlock = async (id) => {
    dispatch(updateStoriesActiveStatus(id));
  };

  const handleDeleteClick = (data) => {
    setBanner(data);
    setVisible(true);
  };

  const deleteBanner = async () => {
    dispatch(deleteStory(banner._id));
  };
  const handlePagination = (value) => {
    dispatch(fetchAllStories({ page: value }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="mb-3 text-end ">
          <CButton
            onClick={() => navigate("/banners/stories/create")}
            color="primary"
            className=""
          >
            Create Story
          </CButton>
        </div>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">No</CTableHeaderCell>
              <CTableHeaderCell> Store Preview</CTableHeaderCell>
              <CTableHeaderCell> Store Name</CTableHeaderCell>
              <CTableHeaderCell> Title</CTableHeaderCell>
              <CTableHeaderCell> Description</CTableHeaderCell>
              <CTableHeaderCell> Button Text</CTableHeaderCell>
              <CTableHeaderCell> Duration</CTableHeaderCell>
              <CTableHeaderCell> Image</CTableHeaderCell>
              <CTableHeaderCell> Redirect Url</CTableHeaderCell>
              <CTableHeaderCell>CreatedBy</CTableHeaderCell>
              <CTableHeaderCell>CreatedAt</CTableHeaderCell>
              <CTableHeaderCell>Expiry Date</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allStories?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  className=" "
                  color={!item.active ? "danger" : ""}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.store?.logo?.secureUrl,
                          })
                        )
                      }
                      width={100}
                      src={item?.store?.logo?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.title}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.description}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.buttonText}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.duration}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.image?.secureUrl })
                        )
                      }
                      width={50}
                      src={item?.image?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item.redirectUrl} />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">
                      {item.createdBy?.name}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(item?.expiryDate)}/{" "}
                      <span className="small">
                        {timeFormatter(item?.expiryDate)}
                      </span>
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <CTooltip content="edit banner details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/banners/stories/edit/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.isActive ? "block " : "unblock "}>
                      <button
                        type="button"
                        className={`${
                          !item.isActive ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        onClick={() => handleBlock(item._id)}
                      >
                        {!item.isActive ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                    <CTooltip content="delete admin">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Deleting mobile story is irreversible. Proceed with caution."}
        setState={deleteBanner}
        visible={visible}
        setVisible={setVisible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllStories;
