import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CButton,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
  cilTrash,
} from "@coreui/icons";
import {
  dateFormatter,
  isDateExpired,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllBanners,
  updateBannerActiveStatus,
  deleteBanner as deleteBannerAction,
  handleOpenModal,
  updateBannerPriority,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllBanners = () => {
  const navigate = useNavigate();
  // password reset
  const [banner, setBanner] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allBanners, page, pages, pageSize, loading } = useSelector(
    (state) => state.banner
  );

  useEffect(() => {
    dispatch(fetchAllBanners({}));
  }, [dispatch]);

  const handleBlock = async (id) => {
    dispatch(updateBannerActiveStatus(id));
  };

  const handleClickPriority = async (bannerId, priority) => {
    // Update the Redux state with the latest number
    const resData = await dispatch(
      updateBannerPriority({ bannerId, priority })
    ).unwrap();

    // Refresh the component if the update is successful
    if (resData?.success) {
      dispatch(fetchAllBanners({}));
      // setRefresh(!refresh);
    }
  };

  const handleDeleteBanner = (data) => {
    setBanner(data);
    setVisible(true);
  };

  const deleteBanner = async () => {
    dispatch(deleteBannerAction(banner._id));
  };
  const handlePagination = (value) => {
    dispatch(fetchAllBanners({ page: value }));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="mb-3 text-end ">
          <CButton
            onClick={() => navigate("/banners/desktop/create")}
            color="primary"
            className="btn btn-sm"
          >
            Create Desktop Banner
          </CButton>
        </div>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">No</CTableHeaderCell>
              <CTableHeaderCell>Desktop Banner</CTableHeaderCell>
              <CTableHeaderCell>Mobile Banner </CTableHeaderCell>
              <CTableHeaderCell>Priority</CTableHeaderCell>
              <CTableHeaderCell> Redirect Url</CTableHeaderCell>
              <CTableHeaderCell>CreatedBy</CTableHeaderCell>
              <CTableHeaderCell>CreatedAt</CTableHeaderCell>
              <CTableHeaderCell>Expiry Date</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allBanners?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  className=" "
                  color={isDateExpired(item?.expiryDate) ? "danger" : ""}
                  // color={"danger"}
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.desktopBanner?.secureUrl,
                          })
                        )
                      }
                      width={100}
                      src={item?.desktopBanner?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>

                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.mobileBanner?.secureUrl,
                          })
                        )
                      }
                      width={100}
                      src={item?.mobileBanner?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="w-75"
                      value={item?.priority}
                      onChange={async (e) => {
                        // Get the latest number from the input
                        const latestNumber = e.target.value.slice(-1);

                        handleClickPriority(item._id, latestNumber);
                      }}
                    />
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <ReadMore text={item.redirectUrl} />
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-capitalize">
                      {item.createdBy?.name}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(item?.expiryDate)}/{" "}
                      <span className="small">
                        {timeFormatter(item?.expiryDate)}
                      </span>
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <CTooltip content="edit admin details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/banners/desktop/edit/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.active ? "block " : "unblock "}>
                      <button
                        type="button"
                        className={`${
                          !item.active ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        onClick={() => handleBlock(item._id)}
                      >
                        {!item.active ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                    <CTooltip content="delete banner">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDeleteBanner(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={
          "Deleting desktop banner is irreversible. Proceed with caution."
        }
        setState={deleteBanner}
        visible={visible}
        setVisible={setVisible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllBanners;
