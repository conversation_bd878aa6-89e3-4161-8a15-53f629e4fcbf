import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilArrowBottom,
  cilFlipToBack,
  cilFlipToFront,
  cilPen,
  cilWifiSignal4,
  cilWifiSignalOff,
} from "@coreui/icons";
import {
  convertUTCtoLocal,
  dateFormatter,
} from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAdminsList,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  updateOfferTrendingStatus,
  fetchAllTrendingOffers,
  updateTrendingPriority,
  handleOpenModal,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";
import { updateOfferMissedStatusTrending } from "src/redux/features/offer.trending";

function TrendingOffers() {
  const navigate = useNavigate();
  const [admin, setAdmin] = useState("");
  const [show, setShow] = useState(false);

  const [partner, setPartner] = useState("");
  const [store, setStore] = useState("");

  const [lowPriority, setLowPriority] = useState(false);
  const [HiPriority, setHiPriority] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(false);
  const [firstExpiry, setFirstExpiry] = useState(false);
  const [title, setTitle] = useState(false);
  const dispatch = useDispatch();

  const { allStoresList } = useSelector((state) => state.store);
  const { adminList } = useSelector((state) => state.admin);
  const { allTrendingOffers, page, pages, pageSize, loading } = useSelector(
    (state) => state.trendingOffer
  );

  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllAdminsList());
    dispatch(fetchAllTrendingOffers({}));
  }, [dispatch]);

  const handleSearch = async (e) => {
    dispatch(fetchAllTrendingOffers({ search: value }));
  };
  const handleSearchOffer = async (value) => {
    dispatch(fetchAllTrendingOffers({ search: value }));
  };

  const handleUpdatePriority = async (offerId, trendingPriority) => {
    dispatch(updateTrendingPriority({ offerId, trendingPriority }));
  };

  const handleFilter = async () => {
    dispatch(fetchAllTrendingOffers({}));
    // currentPage,
    //   store,
    //   titleKey,
    //   offerKey,
    //   partner,
    //   add,
    //   allOffer,
    //   activeOff,
    //   inactOff,
    //   expToday,
    //   title,
    //   lowPriority,
    //   HiPriority,
    //   firstExpiry,
    //   lastUpdated
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllTrendingOffers({ page: value }));
  };
  const handleSort = async (value) => {
    setTitle(false);
    setHiPriority(false);
    setLowPriority(false);
    setFirstExpiry(false);
    setLastUpdated(false);

    if (value === "title") setTitle(true);
    if (value === "prioTrue") setHiPriority(true);
    if (value === "prioFasle") setLowPriority(true);
    if (value === "expire") setFirstExpiry(true);
    if (value === "last") setLastUpdated(true);

    const ti = value === "title" ? true : false;
    const lowPr = value === "prioFasle" ? true : false;
    const hiPr = value === "prioTrue" ? true : false;
    const ex = value === "expire" ? true : false;
    const laU = value === "last" ? true : false;
    //  store,
    //         titleKey,
    //         offerKey,
    //         partner,
    //         add,
    //         allOffer,
    //         activeOff,
    //         expToday,
    //         ti,
    //         lowPr,
    //         hiPr,
    //         ex,
    //         laU
  };
  const handleAddMissedDealClick = async (offerId, count, title) => {};
  const handleRemoveMissedDealClick = async (offerId, count) => {};
  const handleSelectAffiliations = (values) => {
    setPartner(values?.value);
  };
  const handleSelectStores = (values) => {
    setStore(values?.value);
  };
  const handleSelectAdmins = (values) => {
    setAdmin(values?.value);
  };

  // Initialize a ref to hold input refs for each item
  const inputRefs = useRef([]);

  // Ensure inputRefs is filled with refs only once
  if (inputRefs.current.length !== allTrendingOffers.length) {
    // Clear out any existing refs and add new ones
    inputRefs.current = Array(allTrendingOffers.length)
      .fill()
      .map((_, index) => inputRefs.current[index] || React.createRef());
  }

  return (
    <CContainer>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="border rounded p-2">
        <CCol sm={12} xl={6}>
          <label htmlFor="">Cashback title</label>
          <div className="">
            <input
              type="text"
              onChange={handleSearch}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search with cashback title..."
            />
          </div>
        </CCol>
        <CCol sm={12} xl={6}>
          <label htmlFor="">Coupon Code</label>
          <div className="">
            <input
              type="text"
              onChange={handleSearchOffer}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search with coupon code..."
            />
          </div>
        </CCol>
        <CRow>
          <CCol md={4}>
            <SearchAndSelect
              array={adminList}
              handleSelectedValue={handleSelectAdmins}
              placeholder={"Select Admins..."}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allAffiliationsList}
              handleSelectedValue={handleSelectAffiliations}
              placeholder={"Select Partners..."}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allStoresList}
              handleSelectedValue={handleSelectStores}
              placeholder={"Select Stores..."}
            />
          </CCol>
        </CRow>
        <CRow className={` ${show ? " d-none" : " "}   px-2 py-3 my-1`}>
          <CCol className="mt-3 d-flex" sm={12}>
            <button
              type="reset"
              className="btn-danger btn text-light px-5 py-2 me-auto mx-2 btn-sm"
            >
              reset
            </button>
            <button
              onClick={() => handleFilter()}
              type="submit"
              className="btn-primary btn px-5 py-2 mx-2 ms-auto btn-sm "
            >
              filter
            </button>
          </CCol>
        </CRow>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-between  ms-auto   " sm={8}>
          <button
            type="button"
            className={`${
              title ? "border-bottom border-primary border-3 bg-none" : ""
            } border-0 `}
            onClick={() => handleSort("title")}
          >
            Title
            <CIcon
              className={`${title ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              HiPriority ? "border-bottom border-primary border-3 bg-none" : ""
            }  border-0  `}
            onClick={() => handleSort("prioTrue")}
          >
            Highest Priority{" "}
            <CIcon
              className={`${HiPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              lowPriority
                ? "border-bottom border-primary border-3 bg-none"
                : " "
            } border-0  `}
            onClick={() => handleSort("prioFasle")}
          >
            Lowest Priority{" "}
            <CIcon
              className={`${lowPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={` ${
              lastUpdated
                ? "border-bottom border-primary border-3 bg-none "
                : " "
            } border-0 `}
            onClick={() => handleSort("last")}
          >
            Last Modified{" "}
            <CIcon
              className={`${lastUpdated ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={` ${
              firstExpiry
                ? " border-bottom border-primary border-3 bg-none"
                : " "
            }  border-0 `}
            onClick={() => handleSort("expire")}
          >
            Expiring First{" "}
            <CIcon
              className={`${firstExpiry ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="m-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell> NO </CTableHeaderCell>
              <CTableHeaderCell>Product Image</CTableHeaderCell>
              <CTableHeaderCell>Expiry Date</CTableHeaderCell>
              <CTableHeaderCell>TrendingPriority</CTableHeaderCell>
              <CTableHeaderCell>Title</CTableHeaderCell>
              <CTableHeaderCell>Offer</CTableHeaderCell>
              <CTableHeaderCell>Store Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliation</CTableHeaderCell>
              <CTableHeaderCell>Link</CTableHeaderCell>
              <CTableHeaderCell> % / RS </CTableHeaderCell>
              <CTableHeaderCell>Coupon Code </CTableHeaderCell>
              <CTableHeaderCell>Start Date</CTableHeaderCell>
              <CTableHeaderCell>Added By</CTableHeaderCell>
              <CTableHeaderCell>Added Date</CTableHeaderCell>
              <CTableHeaderCell>Edited By</CTableHeaderCell>
              <CTableHeaderCell>Edited Date</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allTrendingOffers?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  key={item._id}
                  color={!item.active ? "danger" : ""}
                >
                  <CTableDataCell>
                    <div>{item?.uid} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.productImage?.secureUrl,
                          })
                        )
                      }
                      width={50}
                      src={item?.productImage?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item.dateExpiry))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* <input
                      //type="number"
                      value={item?.trendingPriority}
                      onChange={(e) =>
                        handleUpdatePriority(item._id, e.target.value)
                      }
                    />
 */}
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="w-75 border rounded p-2"
                      defaultValue={item.trendingPriority || 0} // Set default value
                      ref={inputRefs.current[index]} // Attach ref to the input
                    />

                    <button
                      type="button"
                      className=" my-2 btn btn-primary btn-xs  btn-sm "
                      onClick={() => {
                        const updatedPriority =
                          inputRefs.current[index].current.value; // Read value from the corresponding input
                        handleUpdatePriority(item._id, updatedPriority); // Call the function with item ID and priority
                      }}
                    >
                      Update
                    </button>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.title}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.offer}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell className=" text-center">
                    <div>{item.affiliation.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item.link} />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item.offerPercent}% / {item.offerAmount}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.couponCode}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item.dateStart))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item?.createdAt))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.updatedBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div
                      className={` ${
                        item?.editedDate ? "" : "d-none"
                      } text-center`}
                    >
                      {dateFormatter(convertUTCtoLocal(item?.editedDate))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center ">
                    <CTooltip
                      content={
                        item?.missedDeal
                          ? "remove missed deals"
                          : "add to missed deal"
                      }
                    >
                      {!item?.missedDeal ? (
                        <button
                          type="button"
                          // onClick={() => handleAddMissedDealClick(item._id)}
                          onClick={() =>
                            dispatch(updateOfferMissedStatusTrending(item?._id))
                          }
                          className="border rounded shadow px-2 py-1 text-info m-1"
                        >
                          <CIcon icon={cilFlipToBack} />
                        </button>
                      ) : (
                        <button
                          type="button"
                          // onClick={() =>
                          //   handleRemoveMissedDealClick(item._id, item?.uid)
                          // }
                          onClick={() =>
                            dispatch(updateOfferMissedStatusTrending(item?._id))
                          }
                          className="border rounded shadow px-2 py-1 text-danger m-1"
                        >
                          <CIcon icon={cilFlipToFront} />
                        </button>
                      )}
                    </CTooltip>
                    <CTooltip content="edit offer">
                      <button
                        type="button"
                        onClick={() =>
                          navigate(`/offers/edit-offer/${item._id}`)
                        }
                        className="border rounded shadow px-2 py-1 text-primary m-1 "
                      >
                        <CIcon
                          size="sm"
                          className="w-100"
                          icon={cilPen}
                          title={item.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip
                      content={`${
                        item?.trending ? "remove from " : "add to "
                      } trending`}
                    >
                      <button
                        type="button"
                        onClick={() =>
                          dispatch(updateOfferTrendingStatus(item._id))
                        }
                        className={`${
                          item?.trending ? " text-success" : "text-danger"
                        } border rounded shadow px-2 py-1  m-1 `}
                      >
                        {" "}
                        <CIcon
                          size="sm"
                          className="w-100 "
                          icon={
                            item?.trending ? cilWifiSignal4 : cilWifiSignalOff
                          }
                          title={item.name}
                        />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default TrendingOffers;
