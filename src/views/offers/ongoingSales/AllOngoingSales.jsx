import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import {
  <PERSON>utton,
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
  cilTrash,
} from "@coreui/icons";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  changeActiveStatus,
  deleteOngoingSale,
  fetchAllOngoingSaleOffers,
  handleOpenModal,
  updateOngoingSale,
} from "src/redux/features";
import { CSmartPagination } from "@coreui/react-pro";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllOngoingSales = () => {
  const navigate = useNavigate();
  // password reset
  const [ongoingSale, setOngoingSale] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allOngoingSales, page, pages, pageSize, loading } = useSelector(
    (state) => state.ongoingSale
  );

  useEffect(() => {
    dispatch(fetchAllOngoingSaleOffers({}));
  }, [dispatch]);

  const handleDeleteClick = (data) => {
    setOngoingSale(data);
    setVisible(true);
  };

  const handleDelete = async () => {
    dispatch(deleteOngoingSale(ongoingSale._id));
  };

  const handleBlock = async (item) => {
    const saleId = item?._id;
    dispatch(changeActiveStatus(saleId));
  };

  const handlePagination = (value) => {
    dispatch(fetchAllOngoingSaleOffers({ page: value }));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="mb-3 text-end ">
          <CButton
            onClick={() => navigate("/offers/ongoing-sales/create")}
            color="primary"
            className="btn btn-sm"
          >
            Create Ongoing Sale
          </CButton>
        </div>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">No</CTableHeaderCell>
              <CTableHeaderCell>Sale Logo</CTableHeaderCell>
              <CTableHeaderCell>Sale Name</CTableHeaderCell>
              <CTableHeaderCell>Start Date </CTableHeaderCell>
              <CTableHeaderCell> End Date</CTableHeaderCell>
              <CTableHeaderCell>Offers Count</CTableHeaderCell>
              <CTableHeaderCell>CreatedBy</CTableHeaderCell>
              <CTableHeaderCell>CreatedAt</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allOngoingSales?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  className=" "
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.saleLogo?.secureUrl })
                        )
                      }
                      width={50}
                      src={item?.saleLogo?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-capitalize">{item?.saleName}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(item.saleStartDate)}/
                      {timeFormatter(item?.saleStartDate)}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div>
                      {dateFormatter(item.saleEndDate)}/
                      {timeFormatter(item?.saleEndDate)}{" "}
                    </div>
                  </CTableDataCell>

                  <CTableDataCell>{item.offers?.length}</CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(item?.createdAt)}/{" "}
                      {timeFormatter(item?.createdAt)}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <CTooltip content="edit admin details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/offers/ongoing-sales/edit/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.active ? "block " : "unblock "}>
                      <button
                        type="button"
                        className={`${
                          !item.active ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        onClick={() => handleBlock(item)}
                      >
                        {!item.active ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                    <CTooltip content="delete admin">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={
          "Deleting on going sale offer is irreversible. Proceed with caution."
        }
        setState={handleDelete}
        visible={visible}
        setVisible={setVisible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllOngoingSales;
