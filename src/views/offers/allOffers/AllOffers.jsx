import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilAirplay,
  cilArrowBottom,
  cilFlipToBack,
  cilFlipToFront,
  cilPen,
  cilTrash,
  cilWifiSignal4,
  cilWifiSignalOff,
  cilVolumeHigh,
  cilVolumeOff,
} from "@coreui/icons";
import {
  convertUTCtoLocal,
  dateFormatter,
  isExpired,
} from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAdminsList,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  deleteOffer,
  updateOfferTrendingStatus,
  updateOfferPriority,
  fetchAllOffers,
  handleOpenModal,
  updateOfferMissedStatus,
  updateOfferShareAndEarnStatus,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const offerTypeList = [
  { _id: "all", name: "All Offers" },
  { _id: "active", title: "Active Offers" },
  { _id: "expired", sectionName: "Expired Offers" },
];

function AllOffers() {
  const navigate = useNavigate();
  const location = useLocation();
  const [admin, setAdmin] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [show, setShow] = useState(false);

  const [partner, setPartner] = useState("");
  const [store, setStore] = useState("");
  const [offerKey, setOfferKey] = useState("");
  const [titleKey, setTitleKey] = useState("");

  const [allOffer, setAllOffer] = useState(false);
  const [activeOff, setActiveOff] = useState(true);
  const [inactOff, setInactOff] = useState(false);
  const [expToday, setExpToday] = useState(false);

  const [offerType, setOfferType] = useState("all");

  const [lowPriority, setLowPriority] = useState(false);
  const [HiPriority, setHiPriority] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(false);
  const [firstExpiry, setFirstExpiry] = useState(false);
  const [title, setTitle] = useState(false);
  const [search, setSearch] = useState("");
  const [couponCode, setCouponCode] = useState("");

  const dispatch = useDispatch();

  const { allStoresList } = useSelector((state) => state.store);
  const { adminList } = useSelector((state) => state.admin);
  const {
    allOffers,
    page,
    pages,
    pageSize,
    activeOffers,
    offersExpiredToday,
    expiredOffers,
    allOffersCount,
    loading,
  } = useSelector((state) => state.offer);

  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllAdminsList());
    // dispatch(fetchAllOffers({}));
  }, [dispatch]);
  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       await dispatch(fetchAllAffiliationsList({}));
  //       await dispatch(fetchAllStoresList());
  //       await dispatch(fetchAllAdminsList());
  //       await dispatch(fetchAllOffers({}));
  //     } catch (error) {
  //       console.error("Error fetching data:", error);
  //     }
  //   };

  //   fetchData();
  // }, [dispatch]);

  const handleSearch = async (e) => {
    setSearch(e.target.value);
  };
  const handleSearchOffer = async (e) => {
    setCouponCode(e.target.value);
  };
  const handlePagination = async (value) => {
    dispatch(
      fetchAllOffers({
        page: value,
        store: store?.value,
        affiliation: partner?.value,
        admin: admin.value,
        search,
        couponCode,
        offerType: offerType?.value,
      })
    );
  };

  const handleClickPriority = async (offerId, priority) => {
    // Update the Redux state with the latest number
    const resData = await dispatch(
      updateOfferPriority({ offerId, priority })
    ).unwrap();

    {
      /* // Refresh the component if the update is successful
    if (resData?.success) {
      dispatch(fetchAllOffers({}));

      // setRefresh(!refresh);
    } */
    }
  };

  const handleFilter = async () => {
    const params = {
      store: store?.value,
      affiliation: partner?.value,
      admin: admin?.value,
      search,
      couponCode,
      offerType: offerType?.value,
    };

    updateURL(params);

    dispatch(fetchAllOffers(params));
  };
  const handleClick = (data) => {
    setAllOffer(false);
    setActiveOff(false);
    setInactOff(false);
    setExpToday(false);
    if (data === "All Offers") {
      setAllOffer(true);
    }
    if (data === "All Active Offers") {
      setActiveOff(true);
    }
    if (data === "All Expired Offers") {
      setInactOff(true);
    }
    if (data === "Expiring Today") {
      setExpToday(true);
    }
  };

  const handleSort = async (value) => {
    setTitle(false);
    setHiPriority(false);
    setLowPriority(false);
    setFirstExpiry(false);
    setLastUpdated(false);

    if (value === "title") setTitle(true);
    if (value === "prioTrue") setHiPriority(true);
    if (value === "prioFalse") setLowPriority(true);
    if (value === "expire") setFirstExpiry(true);
    if (value === "last") setLastUpdated(true);

    // Create sort parameter based on selected sort option
    let sortParam = "";
    if (value === "title") sortParam = "title";
    if (value === "prioTrue") sortParam = "highPriority";
    if (value === "prioFalse") sortParam = "lowPriority";
    if (value === "expire") sortParam = "expiry";
    if (value === "last") sortParam = "lastUpdated";

    // Dispatch action to fetch sorted data
    dispatch(
      fetchAllOffers({
        page: page,
        store: store?.value,
        affiliation: partner?.value,
        admin: admin?.value,
        search,
        couponCode,
        offerType: offerType?.value,
        sort: sortParam
      })
    );
  };
  const handleAddMissedDealClick = async (offerId, count, title) => {};
  const handleRemoveMissedDealClick = async (offerId, count) => {};

  const handleSelectAffiliations = (values) => {
    setPartner({ value: values?.value, label: values?.label });
  };
  const handleSelectStores = (values) => {
    setStore({
      label: values?.label,
      value: values?.value,
    });
  };
  const handleSelectAdmins = (values) => {
    setAdmin({
      label: values?.label,
      value: values?.value,
    });
  };
  const handleSelectOfferType = (values) => {
    setOfferType({ value: values?.value, label: values?.label });
  };

  const handleReset = async () => {
    setSearch("");
    setCouponCode("");
    setPartner("");
    setStore("");
    setAdmin("");
    setOfferType("");

    // Clear the search parameters from the URL
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete("store");
    searchParams.delete("affiliation");
    searchParams.delete("admin");
    searchParams.delete("search");
    searchParams.delete("couponCode");
    searchParams.delete("offerType");
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });

    dispatch(
      fetchAllOffers({
        search: "",
        sort: 0,
      })
    );
  };

  // Initialize a ref to hold input refs for each item
  const inputRefs = useRef([]);

  // Ensure inputRefs is filled with refs only once
  if (inputRefs.current.length !== allOffers.length) {
    // Clear out any existing refs and add new ones
    inputRefs.current = Array(allOffers.length)
      .fill()
      .map((_, index) => inputRefs.current[index] || React.createRef());
  }

  const updateURL = (params) => {
    const searchParams = new URLSearchParams(location.search);
    Object.keys(params).forEach((key) => {
      if (params[key]) {
        searchParams.set(key, params[key]);
      } else {
        searchParams.delete(key);
      }
    });
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const initialParams = {
      store: searchParams.get("store") || "",
      affiliation: searchParams.get("affiliation") || "",
      admin: searchParams.get("admin") || "",
      search: searchParams.get("search") || "",
      couponCode: searchParams.get("couponCode") || "",
      offerType: searchParams.get("offerType") || "all",
    };

    const storeItem = allStoresList.find(
      (item) => item._id === initialParams.store
    );
    const affiliationItem = allAffiliationsList.find(
      (item) => item._id === initialParams.affiliation
    );
    const adminItem = adminList.find(
      (item) => item._id === initialParams.admin
    );

    setStore(
      storeItem
        ? {
            value: storeItem._id,
            label: storeItem.name || storeItem.title || storeItem.sectionName,
          }
        : ""
    );
    setPartner(
      affiliationItem
        ? {
            value: affiliationItem._id,
            label:
              affiliationItem.name ||
              affiliationItem.title ||
              affiliationItem.sectionName,
          }
        : ""
    );
    setAdmin(
      adminItem
        ? {
            value: adminItem._id,
            label: adminItem.name || adminItem.title || adminItem.sectionName,
          }
        : ""
    );
    setSearch(initialParams.search);
    setCouponCode(initialParams.couponCode);
    setOfferType(initialParams.offerType);

    dispatch(fetchAllOffers(initialParams));

    // handleFilter(initialParams);
  }, []);

  return (
    <CContainer>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="border rounded p-2">
        <CCol sm={12} xl={6}>
          <label htmlFor="">Cashback title</label>
          <div className="">
            <input
              type="text"
              onChange={handleSearch}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search with cashback title..."
              value={search}
            />
          </div>
        </CCol>
        <CCol sm={12} xl={6}>
          <label htmlFor="">Coupon Code</label>
          <div className="">
            <input
              type="text"
              onChange={handleSearchOffer}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search with coupon code..."
              value={couponCode}
            />
          </div>
        </CCol>
      </CRow>
      <CRow
        className={` ${show ? " d-none" : " "}  border rounded px-2 py-3 my-1`}
      >
        <CRow>
          <CCol md={4}>
            <SearchAndSelect
              array={adminList}
              handleSelectedValue={handleSelectAdmins}
              placeholder={"Select Admins..."}
              defaultValue={admin}
            />
          </CCol>

          <CCol md={4} className="mt-2">
            <SearchAndSelect
              array={offerTypeList}
              handleSelectedValue={handleSelectOfferType}
              placeholder={"Select Offer Type..."}
              defaultValue={offerType}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allAffiliationsList}
              handleSelectedValue={handleSelectAffiliations}
              placeholder={"Select Partners..."}
              defaultValue={partner}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allStoresList}
              handleSelectedValue={handleSelectStores}
              placeholder={"Select Stores..."}
              defaultValue={store}
            />
          </CCol>
        </CRow>
        <CCol className="mt-3 d-flex" sm={12}>
          <button
            onClick={
              () => handleReset()
              // setRefresh(!refresh)
            }
            type="reset"
            className="btn-danger btn text-light px-5 py-2 me-auto mx-2 btn-sm"
          >
            reset
          </button>
          <button
            onClick={() => handleFilter()}
            type="submit"
            className="btn-primary btn px-5 py-2 mx-2 ms-auto btn-sm "
          >
            filter
          </button>
        </CCol>
      </CRow>

      <CRow className=" py-3 my-1">
        <SimpleBox
          click={handleClick}
          shadow={allOffer}
          title={"All Offers"}
          data={allOffersCount}
        />
        <SimpleBox
          click={handleClick}
          shadow={activeOff}
          title={"All Active Offers"}
          data={activeOffers}
        />
        <SimpleBox
          click={handleClick}
          shadow={inactOff}
          title={"All Expired Offers"}
          data={expiredOffers}
        />
        <SimpleBox
          click={handleClick}
          shadow={expToday}
          title={"Expiring Today"}
          data={offersExpiredToday}
        />
      </CRow>
      <CRow>
        <CCol className="ms-auto text-end ">
          <button
            type="button"
            onClick={() => navigate("/offers/create-offer")}
            className=" my-2 btn btn-primary btn-xs  btn-sm "
          >
            Create Offer
          </button>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-between  ms-auto   " sm={8}>
          <button
            type="button"
            className={`${
              title ? "border-bottom border-primary border-3 bg-none" : ""
            } border-0 `}
            onClick={() => handleSort("title")}
          >
            Title
            <CIcon
              className={`${title ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              HiPriority ? "border-bottom border-primary border-3 bg-none" : ""
            }  border-0  `}
            onClick={() => handleSort("prioTrue")}
          >
            Highest Priority{" "}
            <CIcon
              className={`${HiPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              lowPriority
                ? "border-bottom border-primary border-3 bg-none"
                : " "
            } border-0  `}
            onClick={() => handleSort("prioFalse")}
          >
            Lowest Priority{" "}
            <CIcon
              className={`${lowPriority ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={` ${
              lastUpdated
                ? "border-bottom border-primary border-3 bg-none "
                : " "
            } border-0 `}
            onClick={() => handleSort("last")}
          >
            Last Modified{" "}
            <CIcon
              className={`${lastUpdated ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={` ${
              firstExpiry
                ? " border-bottom border-primary border-3 bg-none"
                : " "
            }  border-0 `}
            onClick={() => handleSort("expire")}
          >
            Expiring First{" "}
            <CIcon
              className={`${firstExpiry ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      {/* <CRow> */}
      <div style={{ overflowX: "auto", marginRight: -40 }}>
        <CTable
          align="middle"
          className="m-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell> NO </CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
              <CTableHeaderCell>Product Image</CTableHeaderCell>
              <CTableHeaderCell>Expiry Date</CTableHeaderCell>
              <CTableHeaderCell>Priority</CTableHeaderCell>
              <CTableHeaderCell>Title</CTableHeaderCell>
              <CTableHeaderCell>Offer</CTableHeaderCell>
              <CTableHeaderCell>Store Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliation</CTableHeaderCell>
              <CTableHeaderCell>Link</CTableHeaderCell>
              <CTableHeaderCell> % / RS </CTableHeaderCell>
              <CTableHeaderCell>Coupon Code </CTableHeaderCell>
              <CTableHeaderCell>Start Date</CTableHeaderCell>
              <CTableHeaderCell>Added By</CTableHeaderCell>
              <CTableHeaderCell>Added Date</CTableHeaderCell>
              <CTableHeaderCell>Edited By</CTableHeaderCell>
              <CTableHeaderCell>Edited Date</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allOffers?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  key={item?._id}
                  color={
                    !item?.active || isExpired(item?.dateExpiry) ? "danger" : ""
                  }
                >
                  <CTableDataCell>
                    <div>{item?.uid} </div>
                  </CTableDataCell>

                  <CTableDataCell className="text-center ">
                    <CTooltip
                      content={`${
                        item?.missedDeal ? "remove from " : "add to "
                      } missed deals`}
                    >
                      {!item?.missedDeal ? (
                        <button
                          type="button"
                          // onClick={() => handleAddMissedDealClick(item._id)}
                          onClick={() =>
                            dispatch(updateOfferMissedStatus(item?._id))
                          }
                          className="border rounded shadow px-2 py-1 text-info m-1"
                        >
                          <CIcon icon={cilFlipToBack} />
                        </button>
                      ) : (
                        <button
                          type="button"
                          onClick={() =>
                            dispatch(updateOfferMissedStatus(item?._id))
                          }
                          className="border rounded shadow px-2 py-1 text-danger m-1"
                        >
                          <CIcon icon={cilFlipToFront} />
                        </button>
                      )}
                    </CTooltip>
                    <CTooltip content="edit offer">
                      <button
                        type="button"
                        onClick={() =>
                          navigate(`/offers/edit-offer/${item._id}`)
                        }
                        className="border rounded shadow px-2 py-1 text-primary m-1 "
                      >
                        <CIcon
                          size="sm"
                          className="w-100"
                          icon={cilPen}
                          title={item.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip
                      content={`${item.active ? "delete" : "restore"} offer`}
                    >
                      <button
                        type="button"
                        onClick={() => dispatch(deleteOffer(item._id))}
                        className={`${
                          !item.active ? " text-secondary" : "text-danger"
                        } border rounded shadow px-2 py-1  m-1 `}
                      >
                        {" "}
                        <CIcon
                          size="sm"
                          className="w-100 "
                          icon={cilTrash}
                          title={item.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip
                      content={`${
                        item?.trending ? "remove from " : "add to "
                      } trending`}
                    >
                      <button
                        type="button"
                        disabled={!item?.active}
                        onClick={() =>
                          dispatch(updateOfferTrendingStatus(item?._id))
                        }
                        className={`${
                          item?.trending ? " text-success" : "text-danger"
                        } border rounded shadow px-2 py-1  m-1 `}
                      >
                        {" "}
                        <CIcon
                          size="sm"
                          className="w-100 "
                          icon={
                            item?.trending ? cilWifiSignal4 : cilWifiSignalOff
                          }
                          title={item.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip
                            content={`${
                              item?.shareAndEarn ? "remove from " : "add to "
                            } Share & Earn`}
                          >
                            <button
                              type="button"
                              disabled={!item?.active}
                              onClick={() =>
                                dispatch(updateOfferShareAndEarnStatus(item?._id))
                              }
                              className={`${
                                item?.shareAndEarn ? " text-success" : "text-warning"
                              } border rounded shadow px-2 py-1  m-1 `}
                            >
                              {" "}
                              <CIcon
                                size="sm"
                                className="w-100 "
                                icon={item?.shareAndEarn ? cilVolumeHigh : cilVolumeOff}
                                title={item.name}
                              />
                            </button>
                          </CTooltip>
                  </CTableDataCell>
                          
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({
                            image: item?.productImage?.secureUrl,
                          })
                        )
                      }
                      width={50}
                      src={item?.productImage?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item?.dateExpiry))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="w-75"
                      defaultValue={item.priority || 0} // Set default value
                      ref={inputRefs.current[index]} // Attach ref to the input
                    />

                    <button
                      type="button"
                      className=" my-2 btn btn-primary btn-xs  btn-sm "
                      onClick={() => {
                        const updatedPriority =
                          inputRefs.current[index].current.value; // Read value from the corresponding input
                        handleClickPriority(item._id, updatedPriority); // Call the function with item ID and priority
                      }}
                    >
                      Update
                    </button>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.title}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.offer}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell className=" text-center">
                    <div>{item?.affiliation?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.link} />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">
                      {item?.offerPercent}% / {item?.offerAmount}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.couponCode}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item?.dateStart))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {dateFormatter(convertUTCtoLocal(item?.createdAt))}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.editedBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div
                      className={` ${
                        item?.editedDate ? "" : "d-none"
                      } text-center`}
                    >
                      {dateFormatter(convertUTCtoLocal(item?.editedDate))}
                    </div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </div>
      {/* </CRow> */}
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllOffers;

const Icon = ({ show }) => {
  return (
    <span className={`position-absolute top-0 end-0 ${show ? "" : "d-none"}`}>
      <img
        src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/73/Flat_tick_icon.svg/768px-Flat_tick_icon.svg.png"
        className=" "
        width={25}
        alt=""
      />
    </span>
  );
};

const SimpleBox = ({ show, value, data, title }) => {
  return (
    <CCol
      xs={12}
      sm={6}
      md={4}
      lg={3}
      xl={2}
      className="d-flex justify-content-center align-items-center"
    >
      <div
        className={`border my-2 rounded position-relative bg-white text-center`}
        style={{
          padding: "10px", // Add 10px padding around the content
          width: "100%", // Ensure it takes full width of the column
          minHeight: "100px",
          height: "auto", // Remove fixed height
          display: "flex", // Flexbox to center content
          flexDirection: "column", // Stack content vertically
          justifyContent: "center", // Center content vertically
          alignItems: "center", // Center content horizontally
        }}
      >
        <p className="mt-2 mb-0">{title}</p>
        <p
          className="fw-bold text-center mb-0"
          style={{
            fontSize: 16,
          }}
        >
          {data}
        </p>
      </div>
    </CCol>
  );
};

const Boxes = ({ shadow, data, title, click }) => {
  return (
    <CCol xs={6} sm={3} md={4} xl={2} className="ms-auto ">
      <div
        onKeyUp={() => click(title)}
        className={`border my-2 shadow rounded position-relative bg-white main-buttons btn pb-1 ${
          shadow ? "shadow border border-danger" : " "
        }  text-center `}
      >
        <Icon show={shadow} />
        <div className="border border-info h-50 rounded bg-info d-flex ">
          <CIcon
            icon={cilAirplay}
            height={30}
            className=" text-white m-auto mt-3"
          />
        </div>
        <p className="">{title}</p>
        <p className="fw-bold ">{data}</p>
      </div>
    </CCol>
  );
};
