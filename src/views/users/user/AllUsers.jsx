import React, { useEffect, useState } from "react";
import {
  <PERSON>vatar,
  <PERSON>utton,
  <PERSON>ol,
  CContainer,
  CFormCheck,
  CModal,
  CModalBody,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilChart,
  cilPenNib,
  cilPeople,
  cilThumbDown,
  cilThumbUp,
} from "@coreui/icons";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  updateUserActiveStatus,
  fetchAllUsers,
  writeUserNotes,
  fetchUserStatistics,
} from "src/redux/features";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";
import UserStatistics from "src/components/modals/UserStatistics";
import UserNotes from "src/components/modals/UserNotes";

const AllUsers = () => {
  const [search, setSearch] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [userDetails, setUserDetails] = useState("");
  const [showUserStatistics, setShowUserStatistics] = useState(false);
  const [showUserNoteModel, setShowUserNoteModal] = useState(false);

  const dispatch = useDispatch();
  const { allUsers, page, pages, pageSize, loading, userStatistics } =
    useSelector((state) => state.user);

  useEffect(() => {
    dispatch(fetchAllUsers({}));
  }, [dispatch]);

  const handleChangeRcBlock = async (userId) => {
    dispatch(updateUserActiveStatus(userId));
  };
  const handlePagination = async (value) => {
    dispatch(fetchAllUsers({ page: value, search, startDate, endDate }));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllUsers({ search: value, startDate, endDate }));
  };

  const handleClickUserNote = (user) => {
    setUserDetails(user);
    setShowUserNoteModal(true);
  };

  const handleShowUserStatistics = (userId) => {
    dispatch(fetchUserStatistics(userId));
    setShowUserStatistics(true);
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol className="my-1">
          <input
            type="text"
            className=" border rounded  p-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="">
        <CCol xs={12} md={5} xl={8} className="mb-2">
          <p className="h6">Registration Date :</p>
          <div className="d-flex">
            <div>
              <label htmlFor="">Start Date</label>
              <input
                type="date"
                name=""
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="border rounded  p-2 m-2"
                placeholder=""
                id=""
              />
            </div>
            <div>
              <label htmlFor="">End Date</label>
              <input
                type="date"
                name=""
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="border rounded  p-2 m-2"
                id=""
              />
            </div>
          </div>
        </CCol>
      </CRow>
      {/* <CRow> */}

      <div style={{ overflowX: "auto", marginRight: -40 }}>
        <CTable align="middle" className=" border " striped hover bordered>
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>Actions</CTableHeaderCell>
              <CTableHeaderCell className="text-center">
                <CIcon icon={cilPeople} />
              </CTableHeaderCell>
              <CTableHeaderCell>NO</CTableHeaderCell>
              <CTableHeaderCell>Name</CTableHeaderCell>
              <CTableHeaderCell>Email</CTableHeaderCell>
              <CTableHeaderCell>Mobile</CTableHeaderCell>
              <CTableHeaderCell>R C Blocked</CTableHeaderCell>
              <CTableHeaderCell>User IP</CTableHeaderCell>
              <CTableHeaderCell>Device</CTableHeaderCell>
              <CTableHeaderCell>Referred BY</CTableHeaderCell>
              <CTableHeaderCell>Bank Details</CTableHeaderCell>
              <CTableHeaderCell>Click</CTableHeaderCell>
              <CTableHeaderCell>Orders</CTableHeaderCell>
              <CTableHeaderCell>Notes</CTableHeaderCell>
              <CTableHeaderCell>Registration Date</CTableHeaderCell>
              <CTableHeaderCell>Last Order Date</CTableHeaderCell>
              <CTableHeaderCell>Notified</CTableHeaderCell>
              <CTableHeaderCell>Campaign</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allUsers?.map((item, index) => (
                <CTableRow
                  // color={item.active && "danger"}
                  color={
                    item?.status == "blocked"
                      ? "danger"
                      : item?.status == "inactive"
                      ? "warning"
                      : ""
                  }
                  v-for="item in tableItems"
                  key={index}
                >
                  <CTableDataCell>
                    <CTooltip
                      content={
                        item?.status == "active" || item?.status == "inactive"
                          ? "block"
                          : "unblock"
                      }
                    >
                      <button
                        type="button"
                        onClick={() => handleChangeRcBlock(item._id)}
                        className={`${
                          item?.status === "active"
                            ? "text-danger"
                            : "text-success"
                        } border  rounded shadow px-2 py-1 mx-1 mb-1`}
                      >
                        <CIcon
                          size="sm"
                          className="w-100"
                          icon={
                            item?.status === "active" ||
                            item?.status == "inactive"
                              ? cilThumbDown
                              : cilThumbUp
                          }
                          title={item?.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip content={"add user notes"}>
                      <button
                        type="button"
                        className={` border text-info rounded shadow px-2 py-1 mx-1 mb-1`}
                        onClick={() => handleClickUserNote(item)}
                      >
                        <CIcon
                          size="sm"
                          className="w-100"
                          icon={cilPenNib}
                          title={item?.name}
                        />
                      </button>
                    </CTooltip>
                    <CTooltip content={"user statistics"}>
                      <button
                        type="button"
                        className={` border text-success rounded shadow px-2 py-1 mx-1 mb-1`}
                        onClick={() => handleShowUserStatistics(item._id)}
                      >
                        <CIcon
                          size="sm"
                          className="w-100"
                          icon={cilChart}
                          title={item?.name}
                        />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.uid} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <CAvatar size="md" src={item.avatar?.secureUrl} />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.email}</div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item.mobile} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <CFormCheck
                        onChange={() => handleChangeRcBlock(item._id)}
                        type="radio"
                        name={`flexRadioDefault${index}`}
                        id="flexRadioDefault1"
                        label="No"
                        checked={item?.rcBlock ? false : true}
                      />
                      <CFormCheck
                        onChange={() => handleChangeRcBlock(item._id)}
                        type="radio"
                        name={`flexRadioDefault${index}`}
                        id="flexRadioDefault2"
                        label="Yes"
                        checked={item?.rcBlock ? true : false}
                      />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.userIp} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item.referral ? `${item.referral.uid} / ${item.referral.name} / ${item.referral.email}` : 'N/A'}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.notes}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
        {/* </CRow> */}
      </div>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <UserNotes
        dispatch={dispatch}
        userDetails={userDetails}
        showUserNoteModel={showUserNoteModel}
        setShowUserNoteModal={setShowUserNoteModal}
      />
      <UserStatistics
        userDetails={userStatistics}
        showModal={showUserStatistics}
        setShowModal={setShowUserStatistics}
      />
    </CContainer>
  );
};

export default AllUsers;
