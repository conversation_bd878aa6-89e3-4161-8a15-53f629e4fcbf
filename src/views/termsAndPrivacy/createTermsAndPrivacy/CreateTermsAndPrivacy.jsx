import React, { useState } from "react";
import * as CoreUi from '@coreui/react'
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import MyCkEditor from "src/components/CkEditor/Editor";
import Warning from "src/components/alerts/Warning/Warning";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { createTermsAndPrivacy } from "src/redux/features";

function CreateTermsAndPrivacy() {
    const [content, setContent] = useState(
        `<p>&nbsp;</p><blockquote><p><strong>Terms and Conditions for Indiancashback</strong></p><p><strong>Last updated: 20-10-2024</strong></p><p>Welcome to indiancashback! By accessing and using our services, you agree to comply with and be bound by the following terms and conditions. If you disagree with any part of these terms, please do not use our services.</p><p><strong>1. General Information:</strong></p><p>1.1 <strong>Organization Details:</strong></p><ul><li><strong>Indiancashback</strong> is india's best cashback application. Our registered office is located in <strong>Banglore, India</strong>.</li></ul><p>1.2 <strong>Acceptance of Terms:</strong></p><ul><li>By using our services, you agree to these terms and conditions, including any policies referred to herein.</li></ul><p><strong>2. Use of Services:</strong></p><p>2.1 <strong>Eligibility:</strong></p><ul><li>You must be at least 10 years old to use our services. By using our services, you represent and warrant that you are of legal age.</li></ul><p>2.2 <strong>Account Registration:</strong></p><ul><li>Some services may require account registration. You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.</li></ul><p>2.3 <strong>Prohibited Activities:</strong></p><ul><li>You agree not to engage in any activity that may disrupt or interfere with our services, including but not limited to hacking, spreading malware, or violating any applicable laws.</li></ul><p><strong>3. Intellectual Property:</strong></p><p>3.1 <strong>Ownership:</strong></p><ul><li>All content and materials provided by [Your Organization Name], including but not limited to logos, text, graphics, and software, are the property of [Your Organization Name] and are protected by copyright and other intellectual property laws.</li></ul><p>3.2 <strong>Use of Materials:</strong></p><ul><li>You may not reproduce, distribute, modify, display, or create derivative works of any content provided by [Your Organization Name] without our explicit written permission.</li></ul><p><strong>4. Privacy and Data Protection:</strong></p><p>4.1 <strong>Data Collection:</strong></p><ul><li>We may collect and process personal information in accordance with our Privacy Policy. By using our services, you consent to such collection and processing.</li></ul><p>4.2 <strong>Data Security:</strong></p><ul><li>We implement reasonable security measures to protect your data. However, we cannot guarantee the security of information transmitted over the internet.</li></ul><p><strong>5. Limitation of Liability:</strong></p><p>5.1 <strong>Disclaimer:</strong></p><ul><li>[Your Organization Name] provides services "as is" and does not warrant the accuracy, completeness, or reliability of any content.</li></ul><p>5.2 <strong>Limitation of Liability:</strong></p><ul><li>[Your Organization Name] shall not be liable for any direct, indirect, incidental, consequential, or punitive damages arising out of or relating to your use of our services.</li></ul><p><strong>6. Governing Law and Dispute Resolution:</strong></p><p>6.1 <strong>Governing Law:</strong></p><ul><li>These terms and conditions are governed by the laws of [Jurisdiction]. Any disputes arising under or in connection with these terms shall be subject to the exclusive jurisdiction of the courts in [Jurisdiction].</li></ul><p><strong>7. Changes to Terms and Conditions:</strong></p><p>7.1 <strong>Notification of Changes:</strong></p><ul><li>We may update these terms and conditions to reflect changes in our practices. We will notify you of any significant changes via email or through a prominent notice on our website.</li></ul><p><strong>8. Contact Us:</strong></p><p>If you have any questions or concerns about these terms and conditions, please contact <NAME_EMAIL></p></blockquote>`
    );
    const [type, setType] = useState("terms")
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const handleSubmit = (e) => {
        e.preventDefault();
        if (!content) {
            return toast.custom(
                <Warning message={"please add terms and conditions"} />
            );
        }
        if (!type) {
            return toast.custom(
                <Warning message={"please select type"} />
            );
        }
        const payload = {
            content,
            type
        }
        dispatch(createTermsAndPrivacy(payload))
    };
    const handleSelectType = (values) => {
        setType(values?.value)
    }
    return (
        <CoreUi.CContainer fluid>
            <h4 className="text-center"> Create new Terms And Privacy</h4>
            <CoreUi.CRow className="justify-content-center">
                <CoreUi.CCol lg={12} xl={10} className="border rounded shadow ">
                    <CoreUi.CForm onSubmit={handleSubmit} className=" my-2 p-2">
                        <CoreUi.CRow className="my-3">
                            <CoreUi.CCol md={3} lg={3} xl={3}>
                                <p className="me-3 my-auto">Type</p>
                            </CoreUi.CCol>
                            <CoreUi.CCol md={9} lg={9} xl={9}>

                                <SearchAndSelect
                                    defaultValue={{ label: 'Terms', value: 'terms' }}
                                    array={[{ name: 'Terms', _id: 'terms' }, { name: 'Privacy', _id: 'privacy' }]}
                                    handleSelectedValue={handleSelectType} />
                            </CoreUi.CCol>
                        </CoreUi.CRow>
                        <CoreUi.CRow className="my-3">
                            <CoreUi.CCol md={3} lg={3} xl={3}>
                                <p className="me-3 my-auto">terms and Privacy</p>
                            </CoreUi.CCol>
                            <CoreUi.CCol md={9} lg={9} xl={9}>
                                <MyCkEditor content={content} setData={setContent} />
                            </CoreUi.CCol>
                        </CoreUi.CRow>
                        <div className="mx-auto d-flex mt-3">
                            <CoreUi.CButton
                                className="text-white ms-auto mx-1"
                                onClick={() => navigate("/privacy-policy/all-privacy-policy")}
                                type="reset"
                                color="danger"
                            >
                                Cancel
                            </CoreUi.CButton>
                            <CoreUi.CButton
                                className="text-white me-auto mx-1"
                                type="submit"
                                color="success"
                            >
                                Create{" "}
                            </CoreUi.CButton>
                        </div>
                    </CoreUi.CForm>
                </CoreUi.CCol>
            </CoreUi.CRow>
        </CoreUi.CContainer>
    );
}


export default CreateTermsAndPrivacy