import {
  cilEyedropper,
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
} from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import {
  fetchAllTermsAndPrivacies,
  handleOpenModal,
  updateTermsAndPrivacyStatus,
} from "src/redux/features";

function AllTermsAndPrivacies() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { page, pages, pageSize, allTermsAndPrivacies, loading } = useSelector(
    (state) => state.termsAndPrivacy
  );
  useEffect(() => {
    dispatch(fetchAllTermsAndPrivacies({}));
  }, [dispatch]);

  const handlePagination = (value) => {
    dispatch(fetchAllTermsAndPrivacies({ page: value }));
  };
  const handleDelete = (value) => {
    dispatch(updateTermsAndPrivacyStatus(value));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="text-end">
          <button
            type="button"
            onClick={() => navigate("/terms-and-privacy/create")}
            className=" my-2 btn btn-primary mx-auto btn-sm"
          >
            Create new terms and Privacy
          </button>
        </div>
      </CRow>
      <CRow>
        <CCol>
          <CTable
            align="middle"
            className="mb-0 border"
            hover
            striped
            bordered
            borderColor="secondary"
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="text-center">No</CTableHeaderCell>
                <CTableHeaderCell>type</CTableHeaderCell>
                <CTableHeaderCell>Status</CTableHeaderCell>
                <CTableHeaderCell>createdAt</CTableHeaderCell>
                <CTableHeaderCell>Admin</CTableHeaderCell>
                <CTableHeaderCell>Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allTermsAndPrivacies?.map((item) => (
                  <CTableRow
                    v-for="item in tableItems"
                    className=" "
                    color={!item.active ? "danger" : ""}
                    key={item?.uid}
                  >
                    <CTableDataCell className="text-center">
                      <p>{item?.uid}</p>
                    </CTableDataCell>
                    <CTableDataCell className="position-relative ">
                      <div>{item.type}</div>
                    </CTableDataCell>
                    <CTableDataCell className="position-relative ">
                      <div
                        className={` ${
                          item.active ? "text-success " : " text-danger"
                        }`}
                      >
                        {item.active ? "Active " : "InActive "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="small ">
                        {dateFormatter(item.createdAt)}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.createdBy?.name} </div>
                    </CTableDataCell>
                    <CTableDataCell className="">
                      <CTooltip content="edit terms and conditions details">
                        <button
                          type="button"
                          className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                          onClick={() =>
                            navigate(`/terms-and-privacy/edit/${item._id}`)
                          }
                        >
                          <CIcon icon={cilPencil} />
                        </button>
                      </CTooltip>
                      <CTooltip content={item.active ? "Active " : "InActive "}>
                        <button
                          type="button"
                          className={`${
                            item.active ? "text-success " : " text-danger"
                          } border rounded shadow px-2 py-1 mx-1`}
                          onClick={() => handleDelete(item._id)}
                        >
                          {!item.active ? (
                            <CIcon icon={cilLockLocked} />
                          ) : (
                            <CIcon icon={cilLockUnlocked} />
                          )}
                        </button>
                      </CTooltip>
                      <CTooltip content={"view content"}>
                        <button
                          type="button"
                          className={
                            " border text-success rounded shadow px-2 py-1 mx-1"
                          }
                          onClick={() =>
                            dispatch(handleOpenModal({ content: item.content }))
                          }
                        >
                          <CIcon icon={cilEyedropper} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllTermsAndPrivacies;
