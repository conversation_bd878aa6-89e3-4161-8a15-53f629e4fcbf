import { <PERSON><PERSON>, <PERSON>ontainer, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";

const MultipleSelect = ({
  options,
  setCategories,
  defaultCategories,
  defaultCategoriesType = "objectArray",
}) => {
  const [cat, setCat] = useState([]);

  useEffect(() => {
    if (defaultCategories) {
      let updatedOptions;

      if (defaultCategoriesType === "idArray") {
        updatedOptions = options.map((item) => {
          const categoryDefault = defaultCategories.find(
            (defCat) => defCat.category === item._id
          );

          const updatedSubCategories = item.subCategories.map((subItem) => {
            const isChecked = categoryDefault
              ? categoryDefault.subCategories.includes(subItem._id)
              : false;
            return { ...subItem, checked: isChecked };
          });

          const allChecked = updatedSubCategories.every(
            (subCat) => subCat.checked
          );

          return {
            ...item,
            subCategories: updatedSubCategories,
            checked: allChecked,
          };
        });
      } else if (defaultCategoriesType === "objectArray") {
        updatedOptions = options.map((item) => {
          const updatedSubCategories = item.subCategories.map((subItem) => {
            const isChecked = defaultCategories.some((defCat) => {
              const matchCategory = defCat.category._id === item._id;
              const matchSubCategory = defCat.subCategories.some(
                (defSubCat) => defSubCat._id === subItem._id
              );

              return matchCategory && matchSubCategory;
            });

            return { ...subItem, checked: isChecked };
          });

          const allChecked = updatedSubCategories.every(
            (subCat) => subCat.checked
          );

          return {
            ...item,
            subCategories: updatedSubCategories,
            checked: allChecked,
          };
        });
      }

      setCat(updatedOptions);
    } else {
      setCat(options);
    }
  }, [options, defaultCategories, defaultCategoriesType]);

  // useEffect(() => {
  //   if (defaultCategories) {
  //     const updatedOptions = options.map((item) => {
  //       const updatedSubCategories = item.subCategories.map((subItem) => {
  //         const isChecked = defaultCategories.some((defCat) => {
  //           const matchCategory = defCat.category._id === item._id;
  //           const matchSubCategory = defCat.subCategories.some(
  //             (defSubCat) => defSubCat._id === subItem._id
  //           );

  //           return matchCategory && matchSubCategory;
  //         });

  //         return { ...subItem, checked: isChecked };
  //       });

  //       // Determine if all subcategories are checked
  //       const allChecked = updatedSubCategories.every(
  //         (subCat) => subCat.checked
  //       );

  //       return {
  //         ...item,
  //         subCategories: updatedSubCategories,
  //         checked: allChecked,
  //       };
  //     });

  //     setCat(updatedOptions);
  //   } else {
  //     setCat(options);
  //   }
  // }, [options, defaultCategories]);

  useEffect(() => {
    const selectedCategories = cat.reduce((acc, mainCat) => {
      const selectedSubCategories = mainCat.subCategories
        .filter((subCat) => subCat.checked)
        .map((subCat) => subCat._id);

      if (selectedSubCategories.length) {
        const existingMainCat = acc.find(
          (selectedCat) => selectedCat.category === mainCat._id
        );

        if (existingMainCat) {
          existingMainCat.subCategories.push(...selectedSubCategories);
        } else {
          acc.push({
            category: mainCat._id,
            subCategories: selectedSubCategories,
          });
        }
      }
      return acc;
    }, []);
    setCategories(selectedCategories);
  }, [cat, setCategories]);

  const handleSelectSubCat = (catId, sub, select) => {
    const updatedCategories = cat.map((item) => {
      if (item._id !== catId) {
        return item;
      }

      const updatedSubCategories = item.subCategories.map((data) => {
        if (data._id !== sub) {
          return data;
        }

        return {
          ...data,
          checked: select,
        };
      });

      return {
        ...item,
        subCategories: updatedSubCategories,
      };
    });

    setCat(updatedCategories);
  };
  const handleSelectCat = (arr, select) => {
    const updatedCategories = cat.map((c) => {
      if (c._id !== arr) {
        return c;
      }

      const updatedSubCategories = c.subCategories.map((data) => ({
        ...data,
        checked: select,
      }));

      return {
        ...c,
        checked: select,
        subCategories: updatedSubCategories,
      };
    });

    setCat(updatedCategories);
  };

  return (
    <CContainer
      fluid
      className="w-100 border rounded "
      style={{
        maxHeight: "20rem",
        scrollBehavior: "smooth",
        overflow: "scroll",
      }}
    >
      {cat.map((item) => (
        <CRow key={item._id} className={"my-3"}>
          <CCol md={3}>
            <input
              type="checkbox"
              value={item._id}
              checked={item?.checked}
              onChange={(e) => {
                if (e.target.checked) {
                  handleSelectCat(item._id, true);
                } else {
                  handleSelectCat(item._id, false);
                }
              }}
            />
            <label
              className="fw-bold text-capitalize"
              style={{ fontSize: 12 }}
              htmlFor=""
            >
              {" "}
              {item.name}{" "}
            </label>
          </CCol>
          <CCol md={8}>
            <CRow>
              {item?.subCategories?.map((data) => (
                <CCol key={data._id} className=" my-1" md={6}>
                  <input
                    type="checkbox"
                    className="me-1"
                    checked={data?.checked}
                    value={data._id}
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleSelectSubCat(item._id, data._id, true);
                      } else {
                        handleSelectSubCat(item._id, data._id, false);
                      }
                    }}
                  />
                  <label htmlFor="" style={{ fontSize: 10 }}>
                    {data?.name}
                  </label>
                </CCol>
              ))}
            </CRow>
          </CCol>
          <br />
        </CRow>
      ))}
    </CContainer>
  );
};

export default MultipleSelect;
