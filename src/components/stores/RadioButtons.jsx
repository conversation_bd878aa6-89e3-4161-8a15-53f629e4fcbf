import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CRow } from "@coreui/react";

const RadioButtons = ({ name, title, state, setState }) => {
  return (
    <CRow className="my-3 ">
      <CCol md={3} lg={3} xl={3}>
        <p className="me-3 my-auto">{title}</p>
      </CCol>
      <CCol md={8} lg={8} xl={8} className="flex">
        <CFormCheck
          type="radio"
          name={name}
          id="flexRadioDefault1"
          label="No"
          onChange={() => setState(false)}
          checked={state ? false : true}
        />
        <CFormCheck
          type="radio"
          name={name}
          id="flexRadioDefault2"
          label="Yes"
          onChange={() => setState(true)}
          checked={state}
        />
      </CCol>
    </CRow>
  );
};

export default RadioButtons;
