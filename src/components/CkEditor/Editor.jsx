import React from "react";
import Editor from "ckeditor5-custom-build/build/ckeditor";
import { CKEditor } from "@ckeditor/ckeditor5-react";

const MyCkEditor = ({ content, setData, toolbar, headingOptions }) => {
  return (
    <div className="mx-auto w-100 h-100">
      <CKEditor
        editor={Editor}
        config={{
          toolbar: toolbar ? toolbar : ['|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
          heading: {
            options: headingOptions ? headingOptions : [
              { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
              { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
              { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
              { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
              { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' },
              { model: 'heading5', view: 'h5', title: 'Heading 5', class: 'ck-heading_heading5' },
              { model: 'heading6', view: 'h6', title: 'Heading 6', class: 'ck-heading_heading6' },
            ]
          }
        }}
        data={
          content
            ? content
            : ""
        }
        onReady={(editor) => {
          // console.log(editor, "this is editor");
        }}
        onChange={(event, editor) => {
          const data = editor.getData();
          setData(data);
        }}
        rows={15}
      />
    </div>
  );
};

export default MyCkEditor;