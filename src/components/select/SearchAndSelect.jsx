import React, { useEffect, useState } from "react";
import Select from "react-select";
import makeAnimated from "react-select/animated";
const animatedComponents = makeAnimated();

function SearchAndSelect({
  style,
  array,
  handleSelectedValue,
  placeholder,
  defaultValue,
  clearable,
}) {
  const [options, setOptions] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);

  useEffect(() => {
    if (defaultValue && !selectedOption) {
      setSelectedOption(defaultValue);
    }
    if (array && array.length !== 0) {
      setOptions(
        array.map((item) => {
          return {
            label: item.name || item.title || item.sectionName,
            value: item._id,
          };
        })
      );
    }
  }, [array]);

  // Set selected option when defaultValue changes
  useEffect(() => {
    if (defaultValue) {
      setSelectedOption(defaultValue);
    } else {
      setSelectedOption(null);
    }
  }, [defaultValue]);

  // Clear state on unmount
  useEffect(() => {
    return () => {
      setSelectedOption(null);
    };
  }, []);

  const handleSelectOption = (value) => {
    setSelectedOption(value);
    handleSelectedValue(value);
  };
  return (
    <Select
      search
      isClearable
      components={animatedComponents}
      value={selectedOption}
      onChange={handleSelectOption}
      className={style ? style : "w-100"}
      placeholder={placeholder ? placeholder : "Please Select One..."}
      options={options}
    />
  );
}

export default SearchAndSelect;
