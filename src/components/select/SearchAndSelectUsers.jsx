import React, { useEffect, useState, useCallback } from "react";
import Select from "react-select";
import makeAnimated from "react-select/animated";
import { useDispatch, useSelector } from "react-redux";
import { fetchAllUsers } from "../../redux/features/user";
import debounce from "lodash/debounce";

const animatedComponents = makeAnimated();

function SearchAndSelectUsers({
  style,
  handleSelectedValue,
  placeholder,
  defaultValue,
  pageSize = 10,
}) {
  const dispatch = useDispatch();
  const { allUsers, page, pages, loading } = useSelector((state) => state.user);

  const [options, setOptions] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setCurrentPage(1);
      setOptions([]);
      dispatch(
        fetchAllUsers({
          page: 1,
          pageSize,
          search: searchTerm,
        })
      );
    }, 300),
    []
  );

  // Handle search input
  const handleInputChange = (inputValue) => {
    setSearch(inputValue);
    debouncedSearch(inputValue);
  };

  // Handle scroll and load more
  const handleMenuScrollToBottom = () => {
    if (!loading && currentPage < pages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      dispatch(
        fetchAllUsers({
          page: nextPage,
          pageSize,
          search,
        })
      );
    }
  };

  // Update options when users are fetched
  useEffect(() => {
    if (allUsers?.length) {
      const newOptions = allUsers.map((user) => ({
        label: user.name,
        value: user._id,
        uid: user.uid,
      }));

      if (currentPage === 1) {
        setOptions(newOptions);
      } else {
        setOptions((prev) => [...prev, ...newOptions]);
      }
    }
  }, [allUsers]);

  // Set initial data
  useEffect(() => {
    dispatch(
      fetchAllUsers({
        page: 1,
        pageSize,
        search: "",
      })
    );
  }, []);

  // Handle default value
  useEffect(() => {
    if (defaultValue) {
      setSelectedOption(defaultValue);
    } else {
      setSelectedOption(null);
    }
  }, [defaultValue]);

  // Clear state on unmount
  useEffect(() => {
    return () => {
      setSelectedOption(null);
      setOptions([]);
    };
  }, []);

  const handleSelectOption = (value) => {
    setSelectedOption(value);
    handleSelectedValue(value);
  };

  return (
    <Select
      search
      isClearable
      isSearchable
      components={animatedComponents}
      value={selectedOption}
      onChange={handleSelectOption}
      onInputChange={handleInputChange}
      onMenuScrollToBottom={handleMenuScrollToBottom}
      className={style ? style : "w-100"}
      placeholder={placeholder ? placeholder : "Search Users..."}
      options={options}
      isLoading={loading}
    />
  );
}

export default SearchAndSelectUsers;
