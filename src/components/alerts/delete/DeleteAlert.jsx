import { CButton, CModal, CModalBody } from "@coreui/react";

const DeleteAlert = ({ message, setState, visible, setVisible }) => {

    const handleDelete = (e) => {
        e.preventDefault()
        setVisible(false)
        setState()
    }
    return (
        <CModal visible={visible} onClose={() => setVisible(false)}>
            <CModalBody className="text-center">
                <img src={"https://www.freeiconspng.com/thumbs/alert-icon/alert-icon-red-11.png"} width={100} className="mx-auto" alt="" />
                <p className="h6">{message}</p>
            </CModalBody>
            <div className="d-flex justify-content-center">
                <CButton color="success" className=' mx-1 my-3 text-white' onClick={() => setVisible(false)}>
                    Cancel
                </CButton>
                <CButton color="danger" className=' mx-1 my-3 text-white' onClick={handleDelete}>
                    Delete
                </CButton>
            </div>
        </CModal>
    )
}

export default DeleteAlert