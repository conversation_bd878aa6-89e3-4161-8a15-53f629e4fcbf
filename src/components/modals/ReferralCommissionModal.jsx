import { CButton, CModal, CModalBody } from "@coreui/react";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { closeReferralCommissionModal } from "src/redux/features";

const ReferralCommissionModal = () => {
  const { userId, uid, userName, openModal } = useSelector(
    (state) => state.referralCommissionModal
  );
  const [amount, setAmount] = useState(0);
  const dispatch = useDispatch();
  return (
    <CModal
      visible={openModal}
      onClose={() => dispatch(closeReferralCommissionModal())}
    >
      <CModalBody>
        <div className="d-flex justify-content-around my-3">
          <p>
            User Name:{" "}
            <span className="fw-bold text-capitalize">{userName}</span>
          </p>
          <p>
            User Unique Id: <span className="fw-bold">{uid}</span>
          </p>
        </div>
        <div className="text-center ">
          <label htmlFor="">Amount:</label>{" "}
          <input
            // type="number"
            type="decimal"
            step="0.01"
            className="border rounded px-2  py-1 w-25"
            onChange={(e) => setAmount(e.target.value)}
            value={amount}
          />
        </div>
      </CModalBody>
      <CButton
        color="danger"
        className="w-25 mx-auto my-3 text-white"
        onClick={() => dispatch(closeReferralCommissionModal())}
      >
        Close
      </CButton>
    </CModal>
  );
};

export default ReferralCommissionModal;
