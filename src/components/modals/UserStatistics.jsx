import { CButton, CModal, CModalBody } from "@coreui/react";
import { CModalHeader } from "@coreui/react-pro";
import React from "react";

const UserStatistics = ({ userDetails, showModal, setShowModal }) => {
  return (
    <CModal visible={showModal} onClose={() => setShowModal(false)}>

      <CModalBody>
        <div className="my-1"> User Name: {userDetails?.name}</div>
        <div className="my-1"> User Email: {userDetails?.email}</div>
        <div className="my-1">Withdrawable Balance : {userDetails?.withdrawableBalance}</div>
        <div className="my-1">Total Confirmed Balance : {userDetails?.confirmedBalance}</div>
        <div className="my-1"> Cancelled Balance: {userDetails?.cancelledBalance}</div>
        <div className="my-1"> Pending Balance: {userDetails?.pendingBalance}</div>
        <div className="my-1"> Pending Rewards: {userDetails?.pendingRewards}</div>
        <div className="my-1"> Confirmed Rewards: {userDetails?.confirmedRewards}</div>
        <div className="my-1"> Cancelled Rewards: {userDetails?.cancelledRewards}</div>
        <div className="my-1"> Pending Referral Commission: {userDetails?.pendingReferralCommission}</div>
        <div className="my-1"> Confirmed Referral Commission: {userDetails?.confirmedReferralCommission}</div>
        <div className="my-1"> WithDrawn Balance: {userDetails?.withDrawnBalance}</div>
      </CModalBody>
      <div className="d-flex justify-content-between align-items-center">
        <CButton
          color="danger"
          size="sm"
          className="w-25 mx-auto my-3 text-white"
          onClick={() => setShowModal(false)}
        >
          Close
        </CButton>
      </div>
    </CModal>
  );
};

export default UserStatistics;
