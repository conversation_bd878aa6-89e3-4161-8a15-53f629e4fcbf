import React from "react";
import { CRow, CCol } from "@coreui/react";
const dateNow = new Date().toISOString().slice(0, 16);
function InputField({
  state,
  setState,
  title,
  type,
  error,
  currentDateTime,
  value,
  disable,
  required,
}) {
  return (
    <CRow className="my-5">
      <CCol
        md={3}
        lg={3}
        xl={3}
        className="d-flex justify-content-start align-items-center"
      >
        <p className="me-3 my-auto text-capitalize ">
          {title}
          {required && <span className="text-danger">*</span>}{" "}
        </p>
      </CCol>
      <CCol md={9} lg={9} xl={9} className="position-relative  ">
        <input
          className={`${type === "checkbox" ? "" : "w-100"} border ${
            error && "border-danger"
          } rounded p-2 `}
          placeholder={title}
          type={type === "decimal" ? "text" : type}
          inputMode={type === "decimal" ? "decimal" : undefined}
          pattern={type === "decimal" ? "[0-9]*[.]?[0-9]*" : undefined}
          value={value}
          {...setState(state)}
          min={currentDateTime ? dateNow : ""}
          disabled={disable ? true : false}
        />

        <p style={{ right: 10 }} className=" text-danger  position-absolute">
          {error}
        </p>
      </CCol>
    </CRow>
  );
}

export default InputField;
