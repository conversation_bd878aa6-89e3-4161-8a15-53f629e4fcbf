import React from "react";
import { CRow, CCol } from "@coreui/react";

function SelectField({ state, setState, title, error, array }) {
  return (
    <CRow className="my-4">
      <CCol md={3} lg={3} xl={3}>
        <p className="me-3 my-auto text-capitalize  fs-6">{title}</p>
      </CCol>
      <CCol md={9} lg={9} xl={9} className="position-relative">
        <select
          className={`w-100 border ${error && "border-danger"} rounded p-2  `}
          {...setState(state)}
          placeholder={`select a ${title}`}
        >
          <option></option>
          {array?.map((data, index) => (
            <option key={index} value={data._id}>
              {data?.title || data?.name || data?.storeName}
            </option>
          ))}
        </select>

        <p
          style={{ right: 0 }}
          className=" text-danger py-2 position-absolute "
        >
          {error}
        </p>
      </CCol>
    </CRow>
  );
}

export default SelectField;
