import { CButton, CModal, CModalBody } from "@coreui/react";
import { useDispatch, useSelector } from "react-redux";
import { handleCloseModal } from "src/redux/features";

const PreviewModal = () => {
    const { image, content, openModal } = useSelector((state) => state.previewModal)
    const dispatch = useDispatch()
    return (
        <CModal visible={openModal} onClose={(() => dispatch(handleCloseModal()))}>
            <CModalBody>
                {/* biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation> */}
                {content ? <div dangerouslySetInnerHTML={{ __html: content }} />

                    : <img src={image} width={470} className="" alt="" />}
            </CModalBody>
            <CButton color="danger" className='w-25 mx-auto my-3 text-white' onClick={() => dispatch(handleCloseModal())}>
                Close
            </CButton>
        </CModal>
    )
}

export default PreviewModal;