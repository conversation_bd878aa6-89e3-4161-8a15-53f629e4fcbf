import CIcon from "@coreui/icons-react";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { uploadImage } from "src/utils/fileHandlers";
import Danger from "../alerts/Danger/Danger";

function ImageUploader({ setFile, icon, keyword, accept, label }) {
  const [image, setImage] = useState(null);

  useEffect(() => {
    if (image) {
      uploadImage(image)
        .then((response) => {
          setFile(response);
        })
        .catch((error) => {
          console.log(error);
          toast.custom(<Danger message={"failed to upload the image file"} />);
        });
    }
  }, [image, setFile]);
  const handleImage = (event) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.onload = (event) => {
      // The file's text will be printed here
      setImage(event.target.result);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="">
      <input
        type="file"
        id={label ? label : "upload_image"}
        className={` ${icon ? "d-none" : " "} border  w-50 ${label ? label : "upload_image"}  `}
        onChange={handleImage}
        accept={accept ? accept : ""}
      />
      <label htmlFor={label ? label : "upload_image"}>
        <div
          className={`${icon ? "btn btn-outline-info py-2" : "d-none "
            } d-flex align-items-center justify-content-end `}
        >
          <CIcon icon={icon} />
          <p className="px-2 ">{keyword}</p>
        </div>
      </label>
    </div>
  );
}

export default ImageUploader;
