import React, { useState } from 'react'

function ReadMore({ text }) {
    const [readMore, setReadMore] = useState(false);
    const truncatedContent = readMore ? text : `${text?.slice(0, 50)}...`;

    const handleReadMore = () => {
        setReadMore(!readMore);
    };
    return (
        <div>
            {/* biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation> */}
            <p dangerouslySetInnerHTML={{ __html: truncatedContent }} />
            {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
            <p className='text-info'
                style={{ cursor: 'pointer' }}
                onClick={handleReadMore}>
                {readMore ? "read less... " : 'read more...'}</p>
        </div>
    )
}

export default ReadMore;