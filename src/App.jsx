import React, { Component, Suspense } from "react";
import { Route, Routes, BrowserR<PERSON>er,Hash<PERSON><PERSON>er,MemoryRouter } from "react-router-dom";
import "./scss/style.scss";
import "./app.css";

const loading = (
  <div className="pt-3 text-center">
    <div className="sk-spinner sk-spinner-pulse"></div>
  </div>
);

// Containers
const DefaultLayout = React.lazy(() => import("./layout/DefaultLayout.jsx"));

// Pages
const Login = React.lazy(() => import("./views/pages/login/Login.jsx"));
const Page404 = React.lazy(() => import("./views/pages/page404/Page404.jsx"));
const Page500 = React.lazy(() => import("./views/pages/page500/Page500.jsx"));

class App extends Component {
  render() {
    return (
      <BrowserRouter>
        <Suspense fallback={loading}>
          <Routes>
            <Route exact path="/login" name="Login Page" element={<Login />} />
            <Route exact path="/404" name="Page 404" element={<Page404 />} />
            <Route exact path="/500" name="Page 500" element={<Page500 />} />
            <Route path="*" name="Home" element={<DefaultLayout />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    );
  }
}

export default App;
