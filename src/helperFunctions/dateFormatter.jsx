import moment from "moment-timezone";

export const dateFormatter = (dt) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  const date = new Date(dt);
  const month = months[date.getUTCMonth()];
  const year = date.getFullYear();
  const day = date.getUTCDate();
  const result = `${day} ${month} ${year}`;
  return result;
};

export const isDateExpired = (dateString) => {
  // Parse the provided date string into a Date object
  const providedDate = new Date(dateString);

  // Get the current date in UTC
  const currentDate = new Date();

  // Compare the provided date with the current date
  return providedDate < currentDate; // If true, the date is expired
};

export const timeFormatter = (dt) => {
  const date = new Date(dt);
  let hr = date.getHours();
  let min = date.getMinutes();
  let amPm = "AM";
  if (hr > 12) {
    hr -= 12;
    amPm = "PM";
  }
  if (hr == 12) {
    amPm = "PM";
  }
  if (hr < 10) {
    hr = `0${hr}`;
  }
  if (min < 10) {
    min = `0${min}`;
  }
  const data = `${hr}:${min} ${amPm}`;
  return data;
};

export const dateDeferenceFormatter = (dt1, dt2) => {
  const date1 = new Date(dt1);
  const date2 = new Date(dt2);
  const millisecondsPerSecond = 1000;
  const millisecondsPerMinute = 60 * millisecondsPerSecond;
  const millisecondsPerHour = 60 * millisecondsPerMinute;
  const millisecondsPerDay = 24 * millisecondsPerHour;

  // Calculate the difference in milliseconds
  const differenceInMilliseconds = Math.abs(date2 - date1);

  // Calculate the difference in days, hours, minutes, and seconds
  const days = Math.floor(differenceInMilliseconds / millisecondsPerDay);
  const hours = Math.floor(
    (differenceInMilliseconds % millisecondsPerDay) / millisecondsPerHour
  );
  const minutes = Math.floor(
    (differenceInMilliseconds % millisecondsPerHour) / millisecondsPerMinute
  );
  const seconds = Math.floor(
    (differenceInMilliseconds % millisecondsPerMinute) / millisecondsPerSecond
  );

  // Create the formatted string
  let formattedString = "";
  if (days > 0) {
    formattedString += `${days} day${days > 1 ? "s" : ""}, `;
  }
  formattedString += `${hours} hour${hours > 1 ? "s" : ""}, ${minutes} minute${
    minutes > 1 ? "s" : ""
  }, ${seconds} second${seconds > 1 ? "s" : ""}`;

  return formattedString;
};

export function calculateRemainingPercentage(createdAt, expireDate) {
  const startDate = new Date(createdAt);
  const endDate = new Date(expireDate);
  const currentTime = new Date();

  // Calculate the total time difference in milliseconds
  const totalTimeDifference = endDate.getTime() - startDate.getTime();

  // Calculate the remaining time difference in milliseconds
  const remainingTimeDifference = endDate.getTime() - currentTime.getTime();

  // Calculate the remaining percentage
  const remainingPercentage =
    (remainingTimeDifference / totalTimeDifference) * 100;

  // Calculate the percentage compared to 100%
  const percentageComparedTo100 = 100 - remainingPercentage;

  return percentageComparedTo100;
}

export const dateInDDMMYYYYFormatInCreateOffer = (date, isStart = true) => {
  if (!date) {
    return null;
  }

  const originalDate = new Date(date);
  const year = originalDate.getFullYear();
  const month = (originalDate.getMonth() + 1).toString().padStart(2, "0");
  const day = originalDate.getDate().toString().padStart(2, "0");

  const hours = isStart ? "00" : "23";
  const minutes = isStart ? "01" : "59";

  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export const dateInDDMMYYYYFormat = (date) => {
  if (!date) {
    // console.error("Invalid date provided");
    return null; // Return null or handle invalid date scenario
  }

  // Ensure that the input date has a "T" separator and time part
  const dateTimeParts = date.split("T");
  if (dateTimeParts.length !== 2) {
    console.error("Date format is incorrect");
    return null; // Return null or handle invalid date format scenario
  }

  const [datePart, timePart] = dateTimeParts;
  const [hours, minutes] = timePart.split(":");
  if (!hours || !minutes) {
    console.error("Time part is incorrect");
    return null; // Handle invalid time part
  }

  const originalDate = new Date(`${datePart}T${hours}:${minutes}`);

  const year = originalDate.getFullYear();
  const month = (originalDate.getMonth() + 1).toString().padStart(2, "0");
  const day = originalDate.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export const dateInYYYYMMDDFormat = (date) => {
  const originalDate = new Date(date);

  const year = originalDate.getFullYear();
  const month = (originalDate.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based, so we add 1
  const day = originalDate.getDate().toString().padStart(2, "0");
  const hours = originalDate.getHours().toString().padStart(2, "0");
  const minutes = originalDate.getMinutes().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
};

export const convertUTCtoLocal = (utcDate, timezone = "Asia/Kolkata") => {
  // If no UTC date is provided, return the current time in the local timezone
  if (!utcDate) {
    const currentLocalTime = moment.tz(timezone).format("YYYY-MM-DD HH:mm:ss");
    return currentLocalTime;
  }

  // Convert UTC to the specified timezone
  const localTime = moment.tz(utcDate, timezone).format("YYYY-MM-DD HH:mm:ss");
  return localTime;
};

export function isExpired(dateExpiry) {
  // Convert the expiry date to local time
  const localExpiry = convertUTCtoLocal(dateExpiry);

  // Get the current time in the same timezone
  const currentTime = moment.tz("Asia/Kolkata").format("YYYY-MM-DD HH:mm:ss");

  // Compare the expiry time with the current time
  return moment(localExpiry).isBefore(currentTime);
}
