import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react({
      include: "**/*.{jsx,js}",
    }),
    svgr(),
  ],
  build: {
    outDir: 'build', // Matches CRA's default output directory
    sourcemap: true,
    chunkSizeWarningLimit: 1600,
  },
  resolve: {
    alias: {
      'src': resolve(__dirname, 'src'),
    },
    extensions: ['.js', '.jsx', '.json']
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://admin-api.indiancashback.com',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  define: {
    'global': 'window',
  },
  optimizeDeps: {
    esbuildOptions: {
      loader: {
        '.js': 'jsx',
      },
    },
  },
});
