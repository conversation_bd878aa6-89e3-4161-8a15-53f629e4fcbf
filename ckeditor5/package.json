{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "devDependencies": {"@ckeditor/ckeditor5-autoformat": "^36.0.1", "@ckeditor/ckeditor5-basic-styles": "^36.0.1", "@ckeditor/ckeditor5-block-quote": "^36.0.1", "@ckeditor/ckeditor5-ckbox": "^36.0.1", "@ckeditor/ckeditor5-cloud-services": "^36.0.1", "@ckeditor/ckeditor5-dev-translations": "^32.1.2", "@ckeditor/ckeditor5-dev-utils": "^32.1.2", "@ckeditor/ckeditor5-editor-classic": "^36.0.1", "@ckeditor/ckeditor5-essentials": "^36.0.1", "@ckeditor/ckeditor5-heading": "^36.0.1", "@ckeditor/ckeditor5-image": "^36.0.1", "@ckeditor/ckeditor5-indent": "^36.0.1", "@ckeditor/ckeditor5-link": "^36.0.1", "@ckeditor/ckeditor5-list": "^36.0.1", "@ckeditor/ckeditor5-media-embed": "^36.0.1", "@ckeditor/ckeditor5-paragraph": "^36.0.1", "@ckeditor/ckeditor5-paste-from-office": "^36.0.1", "@ckeditor/ckeditor5-table": "^36.0.1", "@ckeditor/ckeditor5-theme-lark": "^36.0.1", "@ckeditor/ckeditor5-typing": "^36.0.1", "css-loader": "^5.2.7", "postcss": "^8.4.21", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "webpack": "^5.75.0", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack --mode production"}}